<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE fontconfig SYSTEM "../fonts.dtd">
<!-- /etc/fonts/conf.d/57-dejavu-sans-mono.conf

     Define aliasing and other fontconfig settings for
     DejaVu Sans Mono.

     © 2006-2008 Nicolas <PERSON> <nicolas.mailhot at laposte.net>
-->
<fontconfig>
  <!-- Font substitution rules -->
  <alias binding="same">
    <family>Bepa Mono</family>
    <accept>
      <family>DejaVu Sans Mono</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Bitstream Prima Sans Mono</family>
    <accept>
      <family>DejaVu Sans Mono</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>Bitstream Vera Sans Mono</family>
    <accept>
      <family>DejaVu Sans Mono</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>DejaVu LGC Sans Mono</family>
    <accept>
      <family>DejaVu Sans Mono</family>
    </accept>
  </alias>
  <alias binding="same">
    <family><PERSON><PERSON><PERSON></family>
    <accept>
      <family>DejaVu Sans Mono</family>
    </accept>
  </alias>
  <alias binding="same">
    <family>SUSE Sans Mono</family>
    <accept>
      <family>DejaVu Sans Mono</family>
    </accept>
  </alias>
</fontconfig>
