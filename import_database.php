<?php
// Database import script for new_db.sql
include('includes/database.php');

echo "<h2>Database Import Script</h2>\n";
echo "<p>Importing new_db.sql into the 'birth' database...</p>\n";

// Check if the SQL file exists
$sql_file = 'new_db.sql';
if (!file_exists($sql_file)) {
    echo "<div style='color: red;'>❌ Error: SQL file '$sql_file' not found!</div>\n";
    exit;
}

echo "<p>✓ SQL file found: $sql_file</p>\n";

// Read the SQL file
$sql_content = file_get_contents($sql_file);
if ($sql_content === false) {
    echo "<div style='color: red;'>❌ Error: Could not read SQL file!</div>\n";
    exit;
}

echo "<p>✓ SQL file read successfully (" . number_format(strlen($sql_content)) . " characters)</p>\n";

// Set charset to UTF-8 for proper Hindi text support
mysqli_set_charset($conn, "utf8");
echo "<p>✓ Database charset set to UTF-8</p>\n";

// Split SQL content into individual queries
$queries = array_filter(array_map('trim', explode(';', $sql_content)));

echo "<p>✓ Found " . count($queries) . " SQL statements to execute</p>\n";
echo "<hr>\n";

$success_count = 0;
$error_count = 0;

// Execute each query
foreach ($queries as $index => $query) {
    if (empty($query)) continue;
    
    $query_number = $index + 1;
    
    // Show progress for every 10th query or important queries
    if ($query_number % 10 == 0 || stripos($query, 'CREATE TABLE') !== false || stripos($query, 'INSERT INTO') !== false) {
        echo "<p>Executing query $query_number...</p>\n";
        flush(); // Force output to browser
    }
    
    if (mysqli_query($conn, $query)) {
        $success_count++;
        
        // Show success for CREATE TABLE statements
        if (stripos($query, 'CREATE TABLE') !== false) {
            preg_match('/CREATE TABLE\s+`?(\w+)`?/i', $query, $matches);
            $table_name = isset($matches[1]) ? $matches[1] : 'unknown';
            echo "<p style='color: green;'>✓ Created table: $table_name</p>\n";
        }
    } else {
        $error_count++;
        $error = mysqli_error($conn);
        
        // Only show critical errors, skip common warnings
        if (stripos($error, 'already exists') === false && 
            stripos($error, 'Duplicate entry') === false) {
            echo "<p style='color: red;'>❌ Error in query $query_number: $error</p>\n";
            echo "<p style='color: #666; font-size: 12px;'>Query: " . substr($query, 0, 100) . "...</p>\n";
        }
    }
}

echo "<hr>\n";
echo "<h3>Import Summary:</h3>\n";
echo "<p style='color: green;'>✓ Successful queries: $success_count</p>\n";
if ($error_count > 0) {
    echo "<p style='color: orange;'>⚠ Queries with errors/warnings: $error_count</p>\n";
}

// Verify the import by checking some tables
echo "<h3>Database Verification:</h3>\n";

$tables_to_check = ['usertable', 'website', 'portal'];
foreach ($tables_to_check as $table) {
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM `$table`");
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "<p>✓ Table '$table': " . $row['count'] . " records</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ Table '$table': " . mysqli_error($conn) . "</p>\n";
    }
}

// Show all tables in the database
echo "<h3>All Tables in Database:</h3>\n";
$tables_result = mysqli_query($conn, "SHOW TABLES");
if ($tables_result) {
    echo "<ul>\n";
    while ($table_row = mysqli_fetch_array($tables_result)) {
        $table_name = $table_row[0];
        
        // Get row count for each table
        $count_result = mysqli_query($conn, "SELECT COUNT(*) as count FROM `$table_name`");
        $count = 0;
        if ($count_result) {
            $count_row = mysqli_fetch_assoc($count_result);
            $count = $count_row['count'];
        }
        
        echo "<li>$table_name ($count records)</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "<p style='color: red;'>❌ Error getting table list: " . mysqli_error($conn) . "</p>\n";
}

echo "<hr>\n";
echo "<p><strong>Database import completed!</strong></p>\n";
echo "<p>You can now test your application with the new database.</p>\n";

mysqli_close($conn);
?>
