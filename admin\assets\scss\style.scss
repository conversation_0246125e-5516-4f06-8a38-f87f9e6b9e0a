/*-------------------------------------------------------------------
  ===== Table of Contents =====

  * Bootstrap functions
  * Template variables
  * SCSS Compass Functions
  * Boostrap Main SCSS
  * Template mixins
    + Animation Mixins
    + Background Mixins
    + Badge Mixins
    + Button Mixins
    + Miscellaneous Mixins
    + BlockQuote Mixins
    + Cards Mixins
    + Color Functions Mixins
    + Tooltips
    + popovers
  * Core Styles
    + Reset Styles
    + Fonts
    + Functions
    + Backgrounds
    + Typography
    + Miscellaneous
    + Footer
    + Layouts
    + Utilities
    + Demo styles
  * Components
    + Accordions
    + Badges
    + Bootstrap Alerts
    + Boostrap Progress
    + Buttons
    + Breadcrumbs
    + Cards
    + Checkboxes and Radios
    + Dropdowns
    + Forms
    + Google maps
    + Icons
    + Loaders
    + Lists
    + Modals
    + Pagination
    + Popover
    + Preview
    + Tables
    + Tabs
    + Timeline
    + Todo List
    + Tooltips
    + User Profile
    + Pricing table
  * Email
    + Mail Sidebar
    + Mail List Container
    + Message Content
  * Plugin Overrides
    + Ace Editor
    + Avgrund Popup
    + Bootstrap Tour
    + Chartist
    + CodeMirror
    + Colcade
    + Colorpicker
    + Context Menu
    + Data Tables
    + Datepicker
    + Dropify
    + Dropzone
    + Flot chart
    + Full Calendar
    + Google Charts
    + Icheck
    + Jquery File Upload
    + Js-grid
    + Jvectormap
    + Light Gallery
    + Listify
    + No-ui-slider
    + Owl-carousel
    + Progressbar-js
    + Pws-tabs
    + Quill Editor
    + Rating
    + Select2
    + Summernote Editor
    + SweetAlert
    + Switchery
    + Tags
    + TinyMCE Editor
    + Toast
    + Typeahead
    + Wysi Editor
    + X-editable
    + Wizard
  * Landing screens
    + Auth
    + Lock Screen
-------------------------------------------------------------------*/

/*-------------------------------------------------------------------*/
/* === Import Bootstrap functions and variables === */
@import "../../node_modules/bootstrap/scss/functions";
@import "../../node_modules/bootstrap/scss/variables";

/*-------------------------------------------------------------------*/
/* === Import template variables === */
@import "./variables";

/*-------------------------------------------------------------------*/
/* === SCSS Compass Functions === */
@import "../../node_modules/compass-mixins/lib/compass";
@import "../../node_modules/compass-mixins/lib/animate";

/*-------------------------------------------------------------------*/
/* === Boostrap Main SCSS === */
@import "../../node_modules/bootstrap/scss/bootstrap";

/*-------------------------------------------------------------------*/
/* === Template mixins === */
@import "components/mixins/misc";
@import "components/mixins/animation";
@import "components/mixins/background";
@import "components/mixins/badges";
@import "components/mixins/blockqoute";
@import "components/mixins/buttons";
@import "components/mixins/breadcrumbs";
@import "components/mixins/cards";
@import "components/mixins/no-ui-slider";
@import "components/mixins/pagination";
@import "components/mixins/popovers";
@import "components/mixins/tooltips";

/*-------------------------------------------------------------------*/
/* === Core Styles === */
@import "components/reset";
@import "components/fonts";
@import "components/functions";
@import "components/background";
@import "components/typography";
@import "components/misc";
@import "components/footer";
@import "components/utilities";
@import "components/demo";
@import "components/dashboard";

/*-------------------------------------------------------------------*/
/* === Components === */
@import "components/badges";
@import "components/buttons";
@import "components/cards";
@import "components/checkbox-radio";
@import "components/dropdown";
@import "components/forms";
@import "components/icons";
@import "components/loaders/loaders";
@import "components/preview";
@import "components/tables";
@import "components/tabs";
@import "components/todo-list";

/*-------------------------------------------------------------------*/

/*-------------------------------------------------------------------*/
/* === Plugin overrides === */
@import "components/plugin-overrides/ace";
@import "components/plugin-overrides/avgrund";
@import "components/plugin-overrides/codemirror";
@import "components/plugin-overrides/datepicker";
@import "components/plugin-overrides/flot-chart";
@import "components/plugin-overrides/full-calendar";
@import "components/plugin-overrides/jquery-file-upload";
@import "components/plugin-overrides/pws-tabs";
@import "components/plugin-overrides/select2";
@import "components/plugin-overrides/typeahead";
@import "components/plugin-overrides/lists";

/*-------------------------------------------------------------------*/

@import "settings-panel";
@import "navbar";
@import "sidebar";
@import "layouts";
@import "vertical/vertical-wrapper";
