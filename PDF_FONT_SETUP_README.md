# PDF Font Setup Guide

This guide will help you replace the current phpToPDF system with a local PDF generation solution that supports your requested fonts: **Loh Bengali**, **DejaVu Serif**, **Sakal Bharati**, and **DejaVu Serif 1**.

## Current Situation

Your project currently uses phpToPDF (a cloud service) which has limitations:
- Dependency on external service
- Limited font control
- Potential service availability issues

## New Solution: mPDF with Custom Fonts

We're replacing phpToPDF with mPDF, a local PHP PDF library that provides full control over fonts.

## Step-by-Step Setup

### 1. Install Dependencies

First, run the setup script:
```bash
php setup_pdf_fonts.php
```

This will:
- Check if Composer is available
- Install mPDF via Composer
- Create necessary font directories
- Provide detailed setup instructions

### 2. Download Sakal Bharati Font

Since Sakal Bharati is not available on Google Fonts:

1. Download from CDAC: https://www.cdac.in/index.aspx?id=dl_sakal_bharati_font
2. Extract the TTF file from the downloaded archive
3. Rename it to `SakalBharati.ttf` (if needed)
4. Place it in: `fonts/sakal-bharati/SakalBharati.ttf`

### 3. Generate DejaVu Serif TTF Files

Your DejaVu fonts are currently in source format (.sfd files). Generate TTF files:

#### Option A: Using FontForge (Recommended)

1. **Install FontForge:**
   - **Windows**: Download from https://fontforge.org/en-US/downloads/windows/
   - **Linux**: `sudo apt-get install fontforge`
   - **macOS**: `brew install fontforge`

2. **Generate TTF files:**
   ```bash
   php generate_dejavu_fonts.php
   ```

#### Option B: Manual Download (Alternative)

If FontForge installation is problematic, download pre-compiled DejaVu fonts:
1. Visit: https://dejavu-fonts.github.io/
2. Download the TTF package
3. Extract and place the following files in `fonts/dejavu-serif-ttf/`:
   - `DejaVuSerif.ttf`
   - `DejaVuSerif-Bold.ttf`
   - `DejaVuSerif-Italic.ttf`
   - `DejaVuSerif-BoldItalic.ttf`

### 4. Update Your Code

Replace phpToPDF calls with the new system:

#### Old Code:
```php
require("phpToPDF.php");
phptopdf($pdf_options);
```

#### New Code:
```php
require("pdf_generator.php");
generatePDF($pdf_options);
```

### 5. Font Usage

The new system automatically applies your requested font stack:
```css
font-family: "Loh Bengali", "DejaVu Serif", "Sakal Bharati", "DejaVu Serif 1", serif;
```

## File Structure After Setup

```
your-project/
├── fonts/
│   ├── Lohit-Bengali/
│   │   └── Lohit-Bengali.ttf ✓ (already present)
│   ├── dejavu-serif-ttf/
│   │   ├── DejaVuSerif.ttf (generated)
│   │   ├── DejaVuSerif-Bold.ttf (generated)
│   │   ├── DejaVuSerif-Italic.ttf (generated)
│   │   └── DejaVuSerif-BoldItalic.ttf (generated)
│   └── sakal-bharati/
│       └── SakalBharati.ttf (download required)
├── vendor/ (created by Composer)
├── pdf_generator.php ✓ (created)
├── setup_pdf_fonts.php ✓ (created)
├── generate_dejavu_fonts.php ✓ (created)
└── composer.json (created)
```

## Testing

After setup, test with the updated example:
```bash
php admin/crs/type1/examples/generate_pdf_invoice.php
```

## Benefits of New System

1. **Local Control**: No dependency on external services
2. **Custom Fonts**: Full support for your specific font requirements
3. **Better Performance**: No network calls for PDF generation
4. **Reliability**: No service downtime issues
5. **Privacy**: All processing happens locally

## Troubleshooting

### Common Issues:

1. **Composer not found**: Install Composer from https://getcomposer.org/
2. **FontForge not found**: Install FontForge or use pre-compiled DejaVu fonts
3. **Font not displaying**: Check font file paths and permissions
4. **Memory issues**: Increase PHP memory limit in php.ini

### Font Priority

The fonts will be used in this order:
1. **Loh Bengali** - For Bengali text
2. **DejaVu Serif** - Primary Latin font
3. **Sakal Bharati** - For Indian language support
4. **DejaVu Serif 1** - Fallback serif font

## Support

If you encounter issues:
1. Check the error logs
2. Verify all font files are in place
3. Ensure proper file permissions
4. Test with a simple HTML example first

## Next Steps

1. Run `php setup_pdf_fonts.php`
2. Download and place Sakal Bharati font
3. Generate DejaVu TTF fonts
4. Update your existing PDF generation code
5. Test with your existing examples

The new system maintains compatibility with your existing PDF options while providing better font control and reliability.
