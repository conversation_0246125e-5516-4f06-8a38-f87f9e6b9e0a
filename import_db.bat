@echo off
echo Starting database import...
echo.

REM Set MySQL path
set MYSQL_PATH=C:\xampp\mysql\bin

echo Step 1: Dropping existing birth database (if exists)...
"%MYSQL_PATH%\mysql.exe" -u root -e "DROP DATABASE IF EXISTS birth;"
if %errorlevel% neq 0 (
    echo Error dropping database
    pause
    exit /b 1
)
echo Database dropped successfully.

echo.
echo Step 2: Creating new birth database with UTF-8 charset...
"%MYSQL_PATH%\mysql.exe" -u root -e "CREATE DATABASE birth CHARACTER SET utf8 COLLATE utf8_unicode_ci;"
if %errorlevel% neq 0 (
    echo Error creating database
    pause
    exit /b 1
)
echo Database created successfully.

echo.
echo Step 3: Importing new_db.sql file...
echo This may take several minutes due to the large file size (275MB)...
"%MYSQL_PATH%\mysql.exe" -u root --default-character-set=utf8 birth < new_db.sql
if %errorlevel% neq 0 (
    echo Error importing SQL file
    pause
    exit /b 1
)

echo.
echo Step 4: Verifying import...
"%MYSQL_PATH%\mysql.exe" -u root -e "USE birth; SHOW TABLES;"

echo.
echo Database import completed successfully!
echo The birth database now contains the imported data with UTF-8 support for Hindi text.
echo.
pause
