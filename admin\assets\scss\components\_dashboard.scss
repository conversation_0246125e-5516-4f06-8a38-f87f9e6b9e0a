.content-wrapper {
  .page-header {
    h3 {
      span {
        font-size: 15px;
      }
    }
    .btn-header {
      background: $card-bg;
    }
  }
  .survey-head {
    color: $survey-head-color;
    font-size: 14px;
    font-weight: 500;
  }
  .survey-value {
    font-size: 22px;
  }
  .table {
    td {
      img {
        border-radius: 4px;
      }
    }
    .table-user-name {
      small {
        &:before {
          content: "";
          display: inline-block;
          width: 8px;
          height: 8px;
          margin-right: 6px;
          border-radius: 50px;
          background: $success;
          border: 0;
        }
      }
    }
  }
  .font-12 {
    font-size: 12px;
  }
  .survey-img {
    padding-top: 13px;
    height: 63px;
    width: 80px;
    border-radius: 4px;
  }
  .customer-img {
    width: 30px;
    height: 30px;
    border-radius: 4px;
  }
  .card-calender {
    background-image: url("../../assets/images/dashboard/Group 4.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    border-radius: 4px;
    h1 {
      font-size: 22px;
    }
    h5 {
      font-weight: 400;
      padding-bottom: 6px;
    }
    h3 {
      font-size: 40px;
    }
    ul {
      li {
        list-style: none;
        padding: 5px 23px 20px 23px;
        margin: 0 3px;
        &.active {
          border-radius: 3px;
          background-image: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0),
            #ffffff1f
          );
        }
      }
    }
  }
  .card-invoice {
    span {
      font-size: 12px;
      color: $text-muted;
    }
    img {
      height: 30px;
      width: 30px;
      border-radius: 4px;
      @media (max-width: 560px) {
        margin-top: 6px;
      }
    }
    .reload-outer {
      width: 20px;
      height: 20px;
      padding-top: 5px;
      font-size: 10px;
      border-radius: 50px;
      color: $white;
      text-align: center;
    }
    .list-card {
      padding: 1px 0 1px 20px;
      border-radius: 3px;
      box-shadow: $card-shadow-color;
      margin-bottom: 12px;
      background: $list-card-bg;
    }
  }
  .color-card {
    .color-card-head {
      font-weight: 500;
      color: $white;
      font-size: 16px;
      line-height: 1.25;
    }
  }
  .flot-chart-wrapper {
    .flot-chart {
      width: calc(100% + 54px);
      margin-left: -28px;
      height: 276px;
      margin-bottom: 20px;
      .rtl & {
        margin-left: 0;
        margin-right: -28px;
      }
      .flot-text {
        .flot-x-axis {
          .flot-tick-label {
            color: #a7afb7;
            padding-top: 10px;
            font-size: 12px;
            margin-left: 27px;
            @media (max-width: 991px) {
              transform: rotate(45deg);
            }
          }
        }
      }
      @media (max-width: 576px) {
        height: 200px;
      }
    }
  }
  .flot-bar-wrapper {
    .flot-chart {
      height: 51px;
      width: 64px;
    }
  }
  .todo-list-add-btn {
    max-height: 34px;
  }
}
.dropify-wrapper {
  background: inherit;
}
