<?php
/**
 * Modern PDF Generator with Custom Font Support
 * Replacement for phpToPDF with local font control
 * 
 * Supports: <PERSON><PERSON>, <PERSON>ja<PERSON><PERSON> Serif, <PERSON><PERSON>, DejaVu Serif 1
 */

require_once 'vendor/autoload.php'; // You'll need to install mPDF via Composer

use Mpdf\Mpdf;
use Mpdf\Config\ConfigVariables;
use Mpdf\Config\FontVariables;

class CustomPDFGenerator {
    private $mpdf;
    private $fontConfig;
    
    public function __construct($options = []) {
        $this->setupFonts();
        $this->initializeMPDF($options);
    }
    
    /**
     * Setup custom fonts configuration
     */
    private function setupFonts() {
        // Get default font directories
        $defaultConfig = (new ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];
        
        $defaultFontConfig = (new FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];
        
        // Add our custom font directories
        $fontDirs[] = __DIR__ . '/fonts/Lohit-Bengali';
        $fontDirs[] = __DIR__ . '/fonts/dejavu-serif-ttf';
        $fontDirs[] = __DIR__ . '/fonts/sakal-bharati'; // You'll need to create this directory
        
        // Define our custom fonts
        $customFonts = [
            'lohbengali' => [
                'R' => 'Lohit-Bengali.ttf',
                'useOTL' => 0xFF,
                'useKashida' => 75,
            ],
            'dejavuserif' => [
                'R' => 'DejaVuSerif.ttf',
                'B' => 'DejaVuSerif-Bold.ttf',
                'I' => 'DejaVuSerif-Italic.ttf',
                'BI' => 'DejaVuSerif-BoldItalic.ttf',
            ],
            'dejavuserif1' => [
                'R' => 'DejaVuSerif.ttf', // Using same as DejaVu Serif for now
                'B' => 'DejaVuSerif-Bold.ttf',
                'I' => 'DejaVuSerif-Italic.ttf',
                'BI' => 'DejaVuSerif-BoldItalic.ttf',
            ],
            'sakalbharati' => [
                'R' => 'SakalBharati.ttf', // You'll need to place this file
                'useOTL' => 0xFF,
                'useKashida' => 75,
            ]
        ];
        
        // Merge with default fonts
        $fontData = array_merge($fontData, $customFonts);
        
        $this->fontConfig = [
            'fontDir' => $fontDirs,
            'fontdata' => $fontData,
            'default_font' => 'dejavuserif'
        ];
    }
    
    /**
     * Initialize mPDF with custom configuration
     */
    private function initializeMPDF($options = []) {
        $config = array_merge([
            'mode' => 'utf-8',
            'format' => 'A4',
            'default_font_size' => 12,
            'default_font' => 'dejavuserif',
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 16,
            'margin_bottom' => 16,
            'margin_header' => 9,
            'margin_footer' => 9,
            'orientation' => 'P'
        ], $this->fontConfig, $options);
        
        $this->mpdf = new Mpdf($config);
        
        // Set up CSS for our custom fonts
        $this->setupFontCSS();
    }
    
    /**
     * Setup CSS definitions for custom fonts
     */
    private function setupFontCSS() {
        $css = '
        @font-face {
            font-family: "Loh Bengali";
            src: url("fonts/Lohit-Bengali/Lohit-Bengali.ttf");
            font-weight: normal;
            font-style: normal;
        }
        
        @font-face {
            font-family: "DejaVu Serif";
            src: url("fonts/dejavu-serif-ttf/DejaVuSerif.ttf");
            font-weight: normal;
            font-style: normal;
        }
        
        @font-face {
            font-family: "DejaVu Serif";
            src: url("fonts/dejavu-serif-ttf/DejaVuSerif-Bold.ttf");
            font-weight: bold;
            font-style: normal;
        }
        
        @font-face {
            font-family: "DejaVu Serif";
            src: url("fonts/dejavu-serif-ttf/DejaVuSerif-Italic.ttf");
            font-weight: normal;
            font-style: italic;
        }
        
        @font-face {
            font-family: "DejaVu Serif";
            src: url("fonts/dejavu-serif-ttf/DejaVuSerif-BoldItalic.ttf");
            font-weight: bold;
            font-style: italic;
        }
        
        @font-face {
            font-family: "Sakal Bharati";
            src: url("fonts/sakal-bharati/SakalBharati.ttf");
            font-weight: normal;
            font-style: normal;
        }
        
        @font-face {
            font-family: "DejaVu Serif 1";
            src: url("fonts/dejavu-serif-ttf/DejaVuSerif.ttf");
            font-weight: normal;
            font-style: normal;
        }
        
        /* Default font stack as requested */
        body, html {
            font-family: "Loh Bengali", "DejaVu Serif", "Sakal Bharati", "DejaVu Serif 1", serif;
        }
        ';
        
        $this->mpdf->WriteHTML($css, \Mpdf\HTMLParserMode::HEADER_CSS);
    }
    
    /**
     * Generate PDF from HTML content
     */
    public function generateFromHTML($html, $options = []) {
        // Add the font family to the HTML if not already present
        if (strpos($html, 'font-family') === false) {
            $html = $this->addDefaultFontToHTML($html);
        }
        
        $this->mpdf->WriteHTML($html);
        
        return $this->handleOutput($options);
    }
    
    /**
     * Add default font family to HTML content
     */
    private function addDefaultFontToHTML($html) {
        $fontStyle = 'font-family: "Loh Bengali", "DejaVu Serif", "Sakal Bharati", "DejaVu Serif 1", serif;';
        
        // Check if there's already a style tag
        if (preg_match('/<style[^>]*>(.*?)<\/style>/is', $html, $matches)) {
            $existingStyle = $matches[1];
            $newStyle = $existingStyle . "\nbody, html { $fontStyle }";
            $html = str_replace($matches[1], $newStyle, $html);
        } else {
            // Add style tag to head or after opening body tag
            if (strpos($html, '</head>') !== false) {
                $styleTag = "<style>body, html { $fontStyle }</style>";
                $html = str_replace('</head>', $styleTag . '</head>', $html);
            } else {
                $styleTag = "<style>body, html { $fontStyle }</style>";
                $html = $styleTag . $html;
            }
        }
        
        return $html;
    }
    
    /**
     * Handle PDF output based on action
     */
    private function handleOutput($options) {
        $action = isset($options['action']) ? $options['action'] : 'view';
        $filename = isset($options['file_name']) ? $options['file_name'] : 'document.pdf';
        $directory = isset($options['save_directory']) ? $options['save_directory'] : '';
        
        switch ($action) {
            case 'save':
                $filepath = $directory . $filename;
                $this->mpdf->Output($filepath, \Mpdf\Output\Destination::FILE);
                return $filepath;
                
            case 'download':
                $this->mpdf->Output($filename, \Mpdf\Output\Destination::DOWNLOAD);
                break;
                
            case 'view':
            default:
                $this->mpdf->Output($filename, \Mpdf\Output\Destination::INLINE);
                break;
        }
        
        return true;
    }
    
    /**
     * Set header content
     */
    public function setHeader($html) {
        $this->mpdf->SetHTMLHeader($html);
    }
    
    /**
     * Set footer content
     */
    public function setFooter($html) {
        $this->mpdf->SetHTMLFooter($html);
    }
}

/**
 * Compatibility function to replace phptopdf()
 */
function generatePDF($pdf_options) {
    try {
        $generator = new CustomPDFGenerator();
        
        // Handle header and footer if provided
        if (isset($pdf_options['header'])) {
            $generator->setHeader($pdf_options['header']);
        }
        
        if (isset($pdf_options['footer'])) {
            $generator->setFooter($pdf_options['footer']);
        }
        
        // Generate PDF based on source type
        if ($pdf_options['source_type'] === 'html') {
            return $generator->generateFromHTML($pdf_options['source'], $pdf_options);
        } elseif ($pdf_options['source_type'] === 'url') {
            // For URL sources, fetch the content first
            $html = file_get_contents($pdf_options['source']);
            return $generator->generateFromHTML($html, $pdf_options);
        }
        
    } catch (Exception $e) {
        error_log("PDF Generation Error: " . $e->getMessage());
        throw new Exception("Failed to generate PDF: " . $e->getMessage());
    }
}
?>
