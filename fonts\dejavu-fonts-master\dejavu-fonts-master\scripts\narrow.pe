#!/usr/bin/env fontforge
# $Id$

# script file for FontForge for font narrowing
# usage: fontforge -script narrow.pe scale *.sfd
#   created files have suffix .narrow

# DejaVu Condensed have scale 90

if ($version < "20050209")
  Error("Your version of FontForge transforms glyphs w/o content");
endif
if ($argc < 2)
  Error("usage: scale sfds...")
endif
scale = Strtol($argv[1])
i = 2
while (i < $argc)
  Open($argv[i], 1)
  SelectAll()
  Scale(scale, 100, 0, 0)
  # Panose changes are valid only for DejaVu fonts (I guess)
  SetPanose(3, 6)
  new_fontname = $fontname
  old_familyname = $familyname
  j = Strstr(new_fontname, "-Roman")
  if (j > -1)
    new_fontname = Strsub(new_fontname, 0, j)
  endif
  j = Strstr(new_fontname, "-")
  if (j > -1)
    new_fontname = Strsub(new_fontname, 0, j) + "Condensed" + Strsub(new_fontname, j)
  else
    new_fontname = new_fontname + "Condensed"
  endif
  new_fullname = $fullname
  newprefsubfamily = "Condensed"
  new_subfamily = $weight
  j = Strstr(new_fullname, "Bold")
  if (j == -1)
    j = Strstr(new_fullname, "Oblique")
  endif
  if (j == -1)
    j = Strstr(new_fullname, "Italic")
  endif
  if (j > -1)
    new_subfamily = Strsub(new_fullname, j)
    new_fullname = Strsub(new_fullname, 0, j) + "Condensed " + new_subfamily
    newprefsubfamily = newprefsubfamily + " " + new_subfamily
  else
    new_fullname = new_fullname + " Condensed"
  endif
  new_familyname = $familyname + " Condensed"
  SetFontNames(new_fontname, new_familyname, new_fullname)
  SetOS2Value("Width",4)
  SetTTFName(1033, 2, new_subfamily) # ttf_subfamily
  SetTTFName(1033, 3, new_fullname) # ttf_uniqueid
  SetTTFName(1033, 16, old_familyname) # ttf_preffamilyname
  SetTTFName(1033, 17, newprefsubfamily) # ttf_prefmodifiers
  Save($argv[i] + ".narrow")
  i++
endloop
