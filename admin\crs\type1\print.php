<?php
include '../includes/config.php';
error_reporting(0);
mysqli_set_charset($conn,"utf8");
if(isset($_GET['id'])){
//$searchid =$_GET['searchid'];
$searchid = mysqli_real_escape_string($conn,$_GET['id']);
$ecode = mysqli_real_escape_string($conn,$_GET['ecode']);

mysqli_set_charset($conn,"utf8");
$a = mysqli_query($conn,"SELECT * FROM birthmanual Where id='".$searchid."'");
$b = mysqli_fetch_array($a);
$hsptl = $b['hospital'];
mysqli_set_charset($conn,"utf8");
$c = mysqli_query($conn,"SELECT * FROM portal Where hospital='".$hsptl."'");
$f = mysqli_fetch_array($c);
}
$ww = mysqli_query($conn,"SELECT * FROM website Where 1");
$w = mysqli_fetch_array($ww);
$wurl = $w['url'];

?>


<html xmlns="http://www.w3.org/1999/xhtml"><head>
	<meta charset="utf-8">
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="generator" content="pdf2htmlEX">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;700&family=Noto+Sans:wght@400;700&display=swap" rel="stylesheet">
	<style type="text/css">
		
		#sidebar {
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			width: 250px;
			padding: 0;
			margin: 0;
			overflow: auto
		}

		#page-container {
			position: absolute;
			top: 0;
			left: 0;
			margin: 0;
			padding: 0;

		}

		@media screen {
			#sidebar.opened+#page-container {
				left: 250px
			}

			#page-container {
				bottom: 0;
				right: 0;
				overflow: auto
			}

			.loading-indicator {
				display: none
			}

			.loading-indicator.active {
				display: block;
				position: absolute;
				width: 64px;
				height: 64px;
				top: 50%;
				left: 50%;
				margin-top: -32px;
				margin-left: -32px
			}

			.loading-indicator img {
				position: absolute;
				top: 0;
				left: 0;
				bottom: 0;
				right: 0
			}
		}

		.pf {
			position: relative;
			background-color: white;
			overflow: hidden;
			margin: 0;

		}

		.pc {
			position: absolute;
			;
			padding: 0;
			margin: 0;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			overflow: hidden;
			display: block;
			transform-origin: 0 0;
			-ms-transform-origin: 0 0;
			-webkit-transform-origin: 0 0
		}

		.pc.opened {
			display: block
		}

		.bf {
			position: absolute;
			;
			margin: 0;
			top: 0;
			bottom: 0;
			width: 100%;
			height: 100%;
			-ms-user-select: none;
			-moz-user-select: none;
			-webkit-user-select: none;
			user-select: none
		}

		.bi {
			position: absolute;
			;
			margin: 0;
			-ms-user-select: none;
			-moz-user-select: none;
			-webkit-user-select: none;
			user-select: none
		}


		.c {
			position: absolute;
			;
			padding: 0;
			margin: 0;
			overflow: hidden;
			display: block
		}

		.t {
			position: absolute;
			white-space: pre;
			font-size: 1px;
			transform-origin: 0 100%;
			-ms-transform-origin: 0 100%;
			-webkit-transform-origin: 0 100%;
			unicode-bidi: bidi-override;
			-moz-font-feature-settings: "liga"0
		}

		.t:after {
			content: ''
		}

		.t:before {
			content: '';
			display: inline-block
		}

		.t span {
			position: relative;
			unicode-bidi: bidi-override
		}

		._ {
			display: inline-block;
			color: transparent;
			z-index: -1
		}

		::selection {
			background: rgba(127, 255, 255, 0.4)
		}

		::-moz-selection {
			background: rgba(127, 255, 255, 0.4)
		}

		.pi {
			display: none
		}

		.d {
			position: absolute;
			transform-origin: 0 100%;
			-ms-transform-origin: 0 100%;
			-webkit-transform-origin: 0 100%
		}

		.it {
			;
			background-color: rgba(255, 255, 255, 0.0)
		}

		.ir:hover {
			cursor: pointer
		}

	</style>
	<style type="text/css">
		/*! 
 * Fancy styles for pdf2htmlEX
 * Copyright 2012,2013 Lu Wang <<EMAIL>> 
 * https://github.com/pdf2htmlEX/pdf2htmlEX/blob/master/share/LICENSE
 */
		@keyframes fadein {
			from {
				opacity: 0
			}

			to {
				opacity: 1
			}
		}

		@-webkit-keyframes fadein {
			from {
				opacity: 0
			}

			to {
				opacity: 1
			}
		}

		@keyframes swing {
			0 {
				transform: rotate(0)
			}

			10% {
				transform: rotate(0)
			}

			90% {
				transform: rotate(720deg)
			}

			100% {
				transform: rotate(720deg)
			}
		}

		@-webkit-keyframes swing {
			0 {
				-webkit-transform: rotate(0)
			}

			10% {
				-webkit-transform: rotate(0)
			}

			90% {
				-webkit-transform: rotate(720deg)
			}

			100% {
				-webkit-transform: rotate(720deg)
			}
		}

		@media screen {
			#sidebar {
				background-color: #2f3236;
				background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0IiBoZWlnaHQ9IjQiPgo8cmVjdCB3aWR0aD0iNCIgaGVpZ2h0PSI0IiBmaWxsPSIjNDAzYzNmIj48L3JlY3Q+CjxwYXRoIGQ9Ik0wIDBMNCA0Wk00IDBMMCA0WiIgc3Ryb2tlLXdpZHRoPSIxIiBzdHJva2U9IiMxZTI5MmQiPjwvcGF0aD4KPC9zdmc+")
			}

			#outline {
				font-family: Georgia, Times, "Times New Roman", serif;
				font-size: 13px;
				margin: 2em 1em
			}

			#outline ul {
				padding: 0
			}

			#outline li {
				list-style-type: none;
				margin: 1em 0
			}

			#outline li>ul {
				margin-left: 1em
			}

			#outline a,
			#outline a:visited,
			#outline a:hover,
			#outline a:active {
				line-height: 1.2;
				color: #e8e8e8;
				text-overflow: ellipsis;
				white-space: nowrap;
				text-decoration: none;
				display: block;
				overflow: hidden;
				outline: 0
			}

			#outline a:hover {
				color: #0cf
			}

			#page-container {
				background-color: #9e9e9e;
				/*background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjOWU5ZTllIj48L3JlY3Q+CjxwYXRoIGQ9Ik0wIDVMNSAwWk02IDRMNCA2Wk0tMSAxTDEgLTFaIiBzdHJva2U9IiM4ODgiIHN0cm9rZS13aWR0aD0iMSI+PC9wYXRoPgo8L3N2Zz4=");*/
				-webkit-transition: left 500ms;
				transition: left 500ms
			}

			.pf {
				margin: 13px auto;
				box-shadow: 1px 1px 3px 1px #333;
				/*border-collapse: separate*/
			}

			.pc.opened {
				-webkit-animation: fadein 100ms;
				animation: fadein 100ms
			}

			.loading-indicator.active {
				-webkit-animation: swing 1.5s ease-in-out .01s infinite alternate none;
				animation: swing 1.5s ease-in-out .01s infinite alternate none
			}

			.checked {
				background: no-repeat url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAWCAYAAADEtGw7AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3goQDSYgDiGofgAAAslJREFUOMvtlM9LFGEYx7/vvOPM6ywuuyPFihWFBUsdNnA6KLIh+QPx4KWExULdHQ/9A9EfUodYmATDYg/iRewQzklFWxcEBcGgEplDkDtI6sw4PzrIbrOuedBb9MALD7zv+3m+z4/3Bf7bZS2bzQIAcrmcMDExcTeXy10DAFVVAQDksgFUVZ1ljD3yfd+0LOuFpmnvVVW9GHhkZAQcxwkNDQ2FSCQyRMgJxnVdy7KstKZpn7nwha6urqqfTqfPBAJAuVymlNLXoigOhfd5nmeiKL5TVTV+lmIKwAOA7u5u6Lped2BsbOwjY6yf4zgQQkAIAcedaPR9H67r3uYBQFEUFItFtLe332lpaVkUBOHK3t5eRtf1DwAwODiIubk5DA8PM8bYW1EU+wEgCIJqsCAIQAiB7/u253k2BQDDMJBKpa4mEon5eDx+UxAESJL0uK2t7XosFlvSdf0QAEmlUnlRFJ9Waho2Qghc1/U9z3uWz+eX+Wr+lL6SZfleEAQIggA8z6OpqSknimIvYyybSCReMsZ6TislhCAIAti2Dc/zejVNWwCAavN8339j27YbTg0AGGM3WltbP4WhlRWq6Q/btrs1TVsYHx+vNgqKoqBUKn2NRqPFxsbGJzzP05puUlpt0ukyOI6z7zjOwNTU1OLo6CgmJyf/gA3DgKIoWF1d/cIY24/FYgOU0pp0z/Ityzo8Pj5OTk9PbwHA+vp6zWghDC+VSiuRSOQgGo32UErJ38CO42wdHR09LBQK3zKZDDY2NupmFmF4R0cHVlZWlmRZ/iVJUn9FeWWcCCE4ODjYtG27Z2Zm5juAOmgdGAB2d3cBADs7O8uSJN2SZfl+WKlpmpumaT6Yn58vn/fs6XmbhmHMNjc3tzDGFI7jYJrm5vb29sDa2trPC/9aiqJUy5pOp4f6+vqeJ5PJBAB0dnZe/t8NBajx/z37Df5OGX8d13xzAAAAAElFTkSuQmCC)
			}
		}

	</style>
	<style type="text/css">
		.ff0 {
			font-family: 'Noto Sans', 'Noto Sans Devanagari', sans-serif;
			visibility: hidden;
		}

		@font-face {
			font-family: ff1;
			src: url('data:application/font-woff;base64,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')format("woff");
		}

		.ff1 {
			font-family: 'Noto Sans', 'Noto Sans Devanagari', ff1, sans-serif;
			line-height: 0.981943;
			font-style: normal;
			font-weight: normal;
			visibility: visible;
		}

		@font-face {
			font-family: ff2;
			src: url('data:application/font-woff;base64,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')format("woff");
		}

		.ff2 {
			font-family: 'Noto Sans', 'Noto Sans Devanagari', ff2, sans-serif;
			line-height: 1.242204;
			font-style: normal;
			font-weight: normal;
			visibility: visible;
		}

		@font-face {
			font-family: ff3;
			src: url('data:application/font-woff;base64,d09GRgABAAAAAB+gAA4AAAAAOqAAAgAjAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAAAfhAAAABoAAAAcvbmJA0dERUYAAB9oAAAAHAAAAB4AJwA8T1MvMgAAAbAAAABWAAAAVmv21pxjbWFwAAACyAAAAMgAAAGyEAoCS2N2dCAAAAOQAAAABAAAAAQARAURZ2FzcAAAH2AAAAAIAAAACP//AANnbHlmAAAEBAAADskAABOs8iDsi2hlYWQAAAFEAAAALwAAADZJRBJdaGhlYQAAAXQAAAAgAAAAJA2PBmhobXR4AAACCAAAAL8AAADYAZgUFWxvY2EAAAOUAAAAbgAAAG6CBH1ObWF4cAAAAZQAAAAaAAAAIAA6AHVuYW1lAAAS0AAADBIAACFOfI4iynBvc3QAAB7kAAAAeQAAAJ9qf+8teJxjYGRgYADiQrOd/fH8Nl8Z5DkYkMGjGSdF/of+s+BQZhMBcjkYmECiAPvwCOgAeJxjYGRgYBP5Z8nAwKH5P/T/eQ5lBqAICjADAHZtBQh4nGNgZGBgMGNwY2BmAAFGBgTQAxEADgMAsAAAAAEE8QGQAAUAAAUzBZkAAAEeBTMFmQAAA9cAZgISAAACBgYDBQYFAgIE5QAG/1IA+fsKBAAgAAAAAFBmRWQAQAAg5gcGFP45AAAGFAHHYAAAn9/XAAAAAHicLc4vC8JAGAbwh9vuHGIQWTGKySRmmUmEgdWBIBjkzHOfwAVFTWI1Wf2HGFeMBjGJH0IYBjGIZT7Bgx8P73vH+56IUQePWAPSAVJZLIw8VjQXI7hijzbzJLa8z2BMH5rRkmq0owlt/jnk25uMkre8w1c5BKoDX14oZO2xjuEbRSonLflkvwHfKkCrF3oqRCA99r404MwmbHVA1yohLZ3kYUbQ5hFa2tBiCtc8QxsXJvcYFVT5V5hX9H9s/zCUAHicY2BgYGaAYBkGRgYQWAPkMYL5LAwTgLQCELKAaTUGTQYrhgCGUIZwhkiGRIZUhnSGTIZ8hpJn7P//Q1VoMOgwODIEoakoAqn4//j/vf93/9/+f/X/lf+X/1/6f/r/if/H/x/7f/D/fildqO14ASMbA1wZIxOQYEJXAPEKA9DBrEC17BycXNw8vHz8AoJCwiKiEDVi4hKSUtIysnLyCopKyiqqDGrqGpoMWgzaUDN0gFiXQY9BH8wzYGAwNDIm7DS6AQDpXC9DAEQFEQAAACwALAAsACwAmgDAAOIA+gEIASQBMgFqAYIBvAIOAjICcgLAAtYDOgOKA74D5AQuBG4EoATGBOgFKgVUBWoFkgW8BdQF/gYgBmoGnAbgBzQHUAeCB6oH0AgeCFoIsgjcCRIJQAmSCcAJwAnWAAB4nG1YCVgT59aeM5kE9dZKihCXWg0xiShuRAi4UEAIEjZRQEAERdZEICD0aoi4syooKgruW0XQWq3X8lfAfbmtvc91b7W2f7VqrXXr9fpYIB//+WYSuD73HzTJTGbO8p73vN/5wrBMIMOwqeIYRsQ4MGOOAjN28jEHjnnmcVQi/mHyMRGLH5mjInpZTC8fc5BA5+RjQK9rpHKpUi6VB7LDyHDYQjLFMe1Ngdy3DJpkarruS46KXzLvMUEMowSJg8RZInPReGi9xHI1nqmHqVXaCVovrUqt8lRpvTQuMheZVIFn9FsZ3isDL/xa4Sl3VuAVB5mG01tPzRrpDgvzDqXOjmc/I/llWYWflHcxmzZDWFidx4AB+NX5gPi4hab3/fwWZPr6AyhcjSwz1U3V5xjcbJSO10Q1PXnMpkUkJKYdyM0b7rpnfTwaXLXyweqChWmTfScH7EieA45SV0IMPloArU+C7yRfk/V/I5NmG4zGlaa8uV8ywOwmZ9hySX9GyjCAATuxGKczxujEYtRs+asiC4Cl6FW9fyBAoH89OZP97CmkwvznT3NSjKfOkn1k3+mzWQgT2lqHthYJtpw8MWGpIyLgiWBIHR0k6+qm4lH3ylJUZCFnUrLOnoZESDx7ypiS8/Q52U62PX1GbYSQJ3CGecD0YRgZxcxTQwELmeM+Cs71CtZXXwpMTixRHlpclCD4jIfbbAy7EGvOOMmd5fHQSTh24Tb+u1NdD+AS85zpxzC0PBoPmYtzfweJwlWtOqVS+nip8fDyUaoytSPU6hFab5VaraTPMV0B7CHxTWoTNKBg++60/mun+Oaf2ciFkq4H3GrkQj9mKHLBQ0hS4WoDDC1LHQVPNGe2pDY8NDS8dktYGGBZt7wqLSsrffWyrLS0TNfQADMhugEPcogcbmiYf+UK5EDuFTzIelJz5YqQ31uGET1Ff4iH3FMuFXsqkarOIAcgtyCrHMDA3Tm38vd2s0G4v4JhuCC8X44nCqmXQEbn/khakHvio56UiZ4TMGbEFbjcrGxjxqPSEvJP2XukgjUUdAbBmY1+fhGRZVXhEVwe9PH56CMw5Z/5gb+jtJnsy4bztcWWytrYGNAF8z63dt3nCtGnF9aA1syGswNtA/SilCANpDQO2jLU9yjw1LjYwkDoHBQXCoyGpO/N5ujYuhNp8wEqKkl7QY5pQW6f2NjtW+JmJcz+LGn84bhZ04JLSqeF9s0V144a4AJJyQ2lCYlDBxsWNF3NzQMYNFADrutD9PGzth5JTEgH/bSaFcFBfv5LsG6+iMunGCPLM13a244GRVMu1bAXiET0mTXpuXUxW34eBpOHz6FdNKjzEZEYQJktet3Zx0juYq6HMddc5IYareCDNoBlmK76v9Lke10hFVmtCRpdYOyLNeVQWWXNMCzINfWaMb1mW0z0zJjtCVODJsSIb+4j0cM/+ABWl/z6bFWJLZVqfWhmxvkzWRkwdNhU9jmvRWXIv7U8/8YL/LODSCVJ1F1vgeVOyEUlVkPmzIsR3sXWVVJGVq4N1SMhm6fGRi/4YcWyijWPOkrKYOdOGB4RGzvb1NfPf8HZOUm6zIyz6D01reWywci+1KuUNL4HZeWtJyEZSr+7Bf36ffQm1WsCbNhIedCEGC9GbHoxTG9QAIqdVM4ttrZuZN2s0QWif4hz29eLxzdDInYWzWMln8cwxg8ftmfRX4BPZDun4qmlVBHZUKZpef6n1lKyIc6iYxHTq6oj8aiumh7hFhhkNgfpAHT0PdDYgFK4avXrNytXJ81p2hwRBRbz97csRRA5Hbbsio+Li9+1Oy4+Pk70SWl4WFh4aXlYOEB4WDn5TJaeueO6KT/fdH1HZrpsUHxs+fHMjIzM4+Wx8Qxfj2W2POS0HnRpUNsCpHWQsg4SuSsVQTstwJaTnSKiE4ExsQvuFS+rrHj8Z1kJ7NhFfoyMmZWQ29f/4+zTyXPI2So91Q59VWW4PjSc3R6qVEHJ6if3K8rgZBvZS/566zYW4W2al9eG9ZwiI+vMuYzMtNTWS0aDEN911MC7osWCXtlVkHYlBVJKgXTpOb+uHu6jVSsBlCrvicNVeOatEs58hqttKumjRJVkL2hH8F/4TFSqUDTZrtcMI/4H1p5lPkAc5L2xw1E7Nc7YZiK5SOMMtbiyvrRGrGD7HWAda6yP9lmsNZY97BD2YscQg9jYvslggGmk2YDcMKGteLTVh3FhRiPL5QiWFsFSozEbbmqFFD1IcUV1eVds5PAG3I7NmAEzZnxBbnfuCdPrw/Z0EnNrK3v3p4K8yMgtrSlzeYG5Y4iIaDwYEQaGj33Ly/ymgPV15x6DmDHC7DnNJRHTpQPmzd/7Y5ZtbcvveiCuwzqrhCpTtOifFjtM3rPcY3WljkJ1teK6gLqtv9cUWeaaJnjDpcugW1RRvt2kzM67+MyMx7P9C4zjoPKaYYHH+BRTddVxmH37JgyV+5GGkwlxcLCJNJMvGxqnR+6gdaSYPOYxGYiIaDitlxOrVikVNhQ4mYuTI599M8jr6wDq6siPzcTc0sKGQ/HtG3DrFllhgAP7QQte+/ZCey2f6fkLZDfZeeGikCP1MQJ9yBhGI5jl/8ttn+m7qbWVazBb4+Ch2UwGs81maoYcv0reO0kcT1qrr5LjPbbc0Zbz/29L42xqaeFazNZA+BMtOTx7106xwY75CRvmFN1huMo6CnqgldLVkBZimLDkCkUQ91fk5JzvYoqXworl1tyM9ETyG/y0anP9IfL537/++huIXVJeUs/JKMCfHiBHyZcHGwGGfOgNy43W6xFK9Y3rqGzzblyDDz+aTHYIeUgsmMfg7jyoqtneeXpL+WyQX1NbRQx9bbXiP6aVZmS0drKc0djBGAzsCj4rGzbsF2jzL7SSznK7WXhDn6QWDAb+Yby3K4bEstfw3gE4B/AqoqXcsvm36WFTnsh9VFzhuDHwmM1uTUm5qwsJIfVP54/zgNDQO2Ifo7H91NvsHKmjLZ++gr2efIB/603bFbqTYYt3w++fWlexlw6SA9YAWz7fsJ74+q1VY2AvWPcbumvtjDb7voORwBZqaD/PEmOHtJ1U0vvTGMbhq3di6A09cOLKwcdwGT5ne7G9YNPFNmtLK+HWEK6F2un4nlPziHIrOl5w0g6BK/O7Hkjq0KZTt02wBcKbm3/5MlhEiy6yvS4v6gy7xNtJ58YiMou53zq2G3itRL5JpPyaNAor4yK0mMLW0zJ7x+OiQ/80w7DlqPDQxSfgjyVFAEVL/vh3kcVS9O/NxUv5pgfvCaa5xZZ6CLh0AbTanHlLlkRAYwPoQd/Y0NBITpATDY0GbP/c6rUn/la9Nm+eZvzNm2Tv8ap1uSnjPex9/z7f90OortrlT96jfrx4Uw6i8ok3ROCxgXTwnd9yb+VygOUr75GvH7JvoyLr6qZH2jsfR7rT+fnWewbBh2QU+hjKjEQf1KQTlkKkooOatJugLsJCq+yJQfStzmO82+2/kT2Pp2q95927aj2IBX/x3V8LISbWuhSUtcF41JK77MP+7mOjSJAB7la4jbAe5+kNGVltW2fFq6bpNtcG64DODzhb3UP8PXGlkspsXPfEMMbS9u9evHiptQ8LQ8E2GMhx/GrL55IS2z5PSgSviV+cvxkbh0vlv9Itlso8x7i449ujZvpOudx8OS6ayv9Lc/HSaRANjVEzIVhXapzk4z3JL2VuY1tWlotsFDm7fZrOz7/IOMV3ktZ3TvLOO9k5OJQJ/dsfa+KNePWjMzkdIG2KQGuwqo2EmcXDzSSszcC9fgJzya4nHe8JfZKM+0gc9fhKCtxkhbVioP0U0+BQz2BIG/YNJCX+cj8psRVet2IRydfk76tWi3Hr1tlJfjbl5ZlgqAi1pfNX0rR2LVRXwUzqA/cJvRToA2OUK+gUZm/t3sInSd+95DA5vJdYNrBD/gc8IWo3zAJtEzTVcG0dAdR+x1Huqw4fLhEFCLqeYK6RNnt8cwrmBMGiCS9rJdpKVn0Y8iH/ABxZQ1C+2IHsY1SJfSzasLpbb9hmkTSs73IuEHUCqQ2edDyW2eY9J55nlMkS2/gk75mgPW2bCtGIp2UBgRAYUJCv053YSw74RkzPOpqe8Unhd7hxDFmV87G/Y1JSYnzMnYJ8iPgZJk5ebPLzR261Wq8ZFoa6KiB5zu59ibNDFgUGwHjNvNsjXJwgO7c7tgiMbRCjoH3Gz6F0naHbTzrZa22zpoPKFhUX0fksavr2ReGhwza4F35y6vGaKoDK8p9fLFt2TTItpGRpsL4fy/S6jbzx/3jJ57PiAbbWkY6t9Rs3Hf0qOQmS5jbzfok39z36VTPedF/SX0CgZ2CcYp826LDLz2qIPJ2W1f85SXKjlhQ/ykxOjkuWBgZYdoWFaqOizefMFty6nzNHR2khlWwL0VdU6fQAel1VhT5E1NI5+1HREujvpPopc9y4xIQL1Wljxm2r72jfWg8wZlzqp0aypi09FUx5P9wz5aWlnhZqGIIvCeIHyH1sBifVu7tqp26xuAAK+ZjRrnK5fMwYueK3Q9YXR45AmjhvtMLVVTF6LF6Xd4wwGEQzjDbs5yEG3XtqXl7sP53YCWGbW9n5NWGhoWE1G0PojB5a87hyzZqKXx+trcChbq2usPCXXwoXAiyk74XzKeTW+rq6eiup28r3IMYv+gZ9fUjRtguJrEflnO1c+yZLrFaFliFiN6xX92PwnjExi909xnJu5E2kWp2UfIQMwgSis1pzcQPk0IufV+6LGtE21a/uAmrt+sXvWro3Y5RD3frlbCt7fhan05UX+fnRCmxeVTZliiGrdWZiYkqWOCCwoMBnkvvoknWbpgbkF1zOmJcy6e2iiZPGjokPch/pMXrU1ID09dEz0Jrbr+mTJsIItxmBareRo8fqgnO3xMwCmYugXZMwRrk4j84zTgpMWdj/vsN2Sra7u3Y1QxxpGDRQN22YnK3oHRS8bod/gKjFCHpywmidU4b7EPeRyTXYj8FBjQI30kktly7ex//+xjjZflFL55jOhaLBnQ9JrcP7b//Il7gxzP8BoFjRdAAAAHicxVndjtvGFZ51nMCRkyBB0CIoUHggoIUNMNrAbm62QAGuxN0lrJU2FHcdXxUUOZLolUiCpHa9AYo+RC/6Ai36FkHv+wa97QP0pg/Q75wZ/kgrr42kRSxbOpo5P9/5nREthPjF3u/EntB/fi/+bug98dHegaHvift73xj6Paxnhr4P+s+Gfh/094b+QDzc+5ehH4jP7n1u6Ifil/cCQ3/84Z9+9jdDfyJ+8+ivhv5UfPToP4b+TNyXD2Fx7/6H+PY9Wyd6T3yxJw19TzzYOzb0e1i/MPR90H8w9Pug/2LoD8TP9/5h6Aeiu/dvQz8UB/d+ZeiPP//1vT8a+hOxePRbQ38qvnj0T0N/Jh7IPdEXqcjEjchFLOZiIUohxWMRiif4fCq+wusZqCk4pDgETykK/MuFEoFYCQurrkjA3wNliyVeUni1roK/KXwqyFzhPQJnRwxAvYKGC7EGRwjeAFrmzClBk34JLQneM/BMoTcGn4R8CrsB73WE6KfZTR7PF6V8HD6RT7/66pmc3sjDuCzKXAUrS7pJ2JP2cik94iqkpwqVX6mo1xmoV8HFWoaLIJmrQga5knEis/V0GYcySldBnMDAJtIJ+xGLGTa0+ETlMb4dAlYqLkGk6eU7S70j2wVvFNhKOSJPEcNn4mtsqLyI00Q+7T37elPbLV07Lc5YoU5TaVJa2Z6lCcJVIoiCU1kiEQdiH6/I6LiCjh5kU3zmSI5ifTmnsQe9CjJiUZbZwf5+BKVX616RrvNQzdJ8rnqJwvZRC0GV9qr8bpcb7VEpKS5JhaJIxTV4qfj+NyVFxdnZaVknIQDVxny7fTriyx/xIus/RUvujnbjc2yiKHk/4BpYcVQvsZYi82/DQp6dsb4Va2vKWete8J4yfs3ZSsJVGbGeGe+q2prOsK42i3GljDBh+cy0jLaQQmtpMhxzVWhfQhPpSmfJKDb7IgBXyBWSGe2VBuLW2HUlKawXpoK7rSrpcuZINuLPgnGFkAmMf7oGQ1TlirWUvFPFZwZqaer4cY2xsUCTg/CX6AVd52SxiQmtZHhPYWXNOBs0EXtQcq1NsVvybmXjzRYs00shkK1Zi47JNdfAgmdCaSKz4rW2R5X+fKMqNdo1x9BqZYfoFeezynXTvwWkrTf4YdV+7vNckqxZ94PWHZuobmb/bq+ryGm0WV3R5VbVNR5dczxW72Sh6oYZz9TEeKhaFiN+JxsWf1IkXoEjZH2ap13HSzMlqwyFbDtixLFBesDd6RupABpTngxNDtqzqInA7UmQgL803VBs8Fa90kSsPQPacpJ9DkympvXcrmpNR0NP8uCOfKZ8BkmT+xV/NvPjXXJRwvOMz7XAeNTbiNRdshSTmxr/irsv5l6uJhphL83U0ysaKcU0auW8XXXV+UVWdLzW0BKwXOVRxEgpX0krGnPwkTcLs5a3ZmjA1aNrt7KxHZ/irT61Z1y0UWEB52gXgruRbNrbjssujJbJ+5Ll4jumem4mkGJ8qw291UpRV2bVN9uniDLzTm1k4Jq9ili+u+Nc7NZ+b0sQf3XqdlvVpntnuHXOTLnv0xbWtemHKhNX2I13REyJ1xznxHR0hpc+xQKerKqWaOdfY767YxY86SV/Fgaj4op6c71o73bNcNpdM9dmhHdFVbYi187hD+3ZgqdndWY3XVd1FN0glvUdJDcSmxozruhLvM9NxvS5mHBst+8f/4+J9WavpqZHSnMuzupInQiH7YzFCN/IzhjffPEC90mP91ysSdznPOxc4NsAqwPOi807tN/lbnwBmjSOxTnr0jo8vJPul1gh3ZK/07fn4B9BF8k64lu24UDbhDk91n2K1SE+HcNHEn2snOM70ceCbqPa3ghSPvcOyREWjdTHemN1E5XLFitkp/jmQf+J2bWh22V9hN/iSBE9qnEeGaQ2x4g0k84+EA35G62e4/MMfBOOp80+a7Qj9uEI+9oXhxHoTGhEfXyewTZxHAOXzyjIkm84LfaQ/BmwPFl9zqsa2dhkmehGS8/EUuOg+F/Ulifs/xAvyf77WPE5Nzb0V3qr2jlmDad1HZ2zfzbHYcwWDnmPokjxHNacXisrfY4X5Y2QD9iSzRGZ7PSk0raZnV3VUVk4Zv8cjtSQuSeIowN+t17R9eiyr30TW61T172uiWErun32kTL7Daw6pqZsjt2mF7pDCH/jhc6Abd77rZg12R+Z7PbrXI+5ym5H5QX3osNcNud6UkfhiPv31CA/b1VYlcdzU5/jGtlmfKs+qvjeZXZoXZXtzQwOuJ6GBuGkjsbb9erZ5eBcC/n3TlnP7c2Tu317bG6l7fun1Zq17ZuAnsLHzLva4mtW9XzWZ1bzm6d9h9t1clW/kvWdvrn9VrcPPbv1b6P27Tfie7q+Cxb1rUSfH2l9M7nm3eZM178GV8zR/r1XsF3t2dpIbOvS98uAbwtkrdgRzbtOqO1fiBmf99rKNdOluZmQf2vDS+vfbf0qzrd+Vb0tB5Uvb4t/zvnOzG+qmCNM98me0ZuL6vdZExOKgH76tdrKelN9pO1AbN9DKQbzFvLIZFw/SSObHSGO+GEcPaKkx5z14035uFBKTtUyvX7Sk+/wQLPX6TTCFyoPpNZcP0btfHnnn07nhz9wlVuWY0CUZR5EahXklzKdbWvpdM5UvooLfsQJ7oXKFWzN8yApVWTJWQ7nIQaH87myZJnKILmRmcoLCKTTEg7HyRxWQoAmznKhzHPNIAzTVQZ2YigX0I4gqaRAgLscku4TKItkUBRpGAewhwiG65VKyqAkPLN4iRg/Jo0sICfprLxGzLtPGEmusjyN1qFiNVEMx+LpulSMYUPAQpbC5ToiJNdxuUjXJcCsYmOI+HMdSqhdF+Andyy5Uuw157dYWC0bFtncT3NZKOQB3DGgGve3TBM4qM0o0KUJHRu6XqSr2wKUhtk6T2BQsWCUyiK1ZLGevlJhSSs6xkuUJDkUpkkUkx/FQafjYyuYpleKPdBVxADqIkjSEmko9CplJWsqQO/JYhHAqakyUQMMFHmw4WeaoC5yuUpztdNtWd5kahbAUE+D2txdBTekf5VG8SymQguWJUoPBJQGUcSe69BRfwU5cK2XQc6GIlXE84RhzJc32aIgIarQIISSgiQqPMW2JV1xkQ5YsGwp2FJi5CosjUZATJY3Mt4odbiUqyRYaV4iCgom5aZqEYW6U9qB6zSPCtmte7FLtqsN2aXW7XLYkJ2h6ZmpQjeR1jXyQE5cpXENTL0u0TUyyDK0WDBdKtrQ/kPzVmIWQSkXQQGNKtmMC8w1FR7JdRIZwN3NudLVHt6V2SJdUmdz6ihRgVzSBEG/VIxZEF4GcziGXkzSen68e2FtmMLQAkS1nBGoE0cejUe+nIyP/Be250h3Is+88YU7cAaya0/wvWvJF65/Mj73JTg8e+S/lOMjaY9eyufuaGBJ59szz5lM5NiT7unZ0HWw5o76w/OBOzqWh5AbjX05dE9dH0r9MYsaVa4zIWWnjtc/wVf70B26/ktLHrn+iHQeQaktz2zPd/vnQ9uTZ+fe2XjiQMcAakfu6MiDFefUgRNQ1B+fvfTc4xPfgpCPRUv6nj1wTm3vuUUIx3DZk8zSA0rokM4FCU9O7OFQHrr+xPcc+5R4KTrHo/Epxeh8NLB9dzyShw5csQ+HjsYGV/pD2z215MA+tY+dSWOE2Iw7TThI4NgZOZ49tOTkzOm7RCCOruf0feZE7BGJIcPtj0cT55tzLICvMoGEnDhsAg7Y+NtnZOz+CO6SHn/s+TWUF+7EsaTtuROCcOSNAZfyCQny8RzxpOSNDF7KEa3drg5wkbRxcODYQyicEIxbvKgu53WospJq2zS3Ho88SvX8tLhq9RBACR8naFy9xiTqGZ3FJ4+ecE1z0ZFsmfFL4wPVjdNIj9/oSmEKFjRK0B8pDZPruOBOxzG4Ss25VwRLGINUzYV5GSwhVtQwNxuqOhCzPIbIdR6XGCYyWGM1j78zR3FujqptD8jKNv5cFRlOqvhKLW964M3pPGMkcTJL85VxncMXlgfVDC3lnJVHcDzN5z3Z+TH/K7rPt+BL/Nvnm2PEz+N6/Gw0w9rmc767/w91/zq+jPdjjMPXvWyR7ZuZ/AP/5/q/4N0vugAAeJx9zLtuQQEAgOHvHAMNCUXdbXVXDuISMZKIYJBgNhgMbH3/eoL+ybf+Qv83ewuEYj4kpaRlfMrKyftSUFRSVlFVU/etoamlraOrp+/HwFBkZGxi+v7NLSytrG3t7J2cXVyDMIjFf1+PzSyaJ4635/1wH0R//2EM/AAAAAAAAAH//wACeJxjYGRgYOABYjEgZmJgBEJTIGYB8xgABeUAZHicY2BgYGQAgtsK+98xIIFHM06KAAA34wUBAAA=')format("woff");
		}

		.ff3 {
			font-family: 'Noto Sans', 'Noto Sans Devanagari', ff3, sans-serif;
			line-height: 0.981957;
			font-style: normal;
			font-weight: normal;
			visibility: visible;
		}

		@font-face {
			font-family: ff4;
			src: url('data:application/font-woff;base64,d09GRgABAAAAAAaoAA4AAAAACswAAgBbAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAAAGjAAAABoAAAAcvbmJA0dERUYAAAZwAAAAHAAAAB4AJwAMT1MvMgAAAawAAABKAAAAYPlYwGhjbWFwAAACEAAAAEYAAAFKy/Yg/mN2dCAAAAJYAAAABAAAAAQARAURZ2FzcAAABmgAAAAIAAAACP//AANnbHlmAAACbAAAAJUAAACgK0EAJWhlYWQAAAFEAAAALwAAADZJVxVZaGhlYQAAAXQAAAAaAAAAJA6RCFpobXR4AAAB+AAAABgAAAAYGMAC2mxvY2EAAAJcAAAADgAAAA4AqACSbWF4cAAAAZAAAAAaAAAAIAAJADduYW1lAAADBAAAAzYAAAZm/uRwuXBvc3QAAAY8AAAAKQAAAD9mROtKeJxjYGRgYABip2PsH+P5bb4yyHMwIINHM06KMLgwMLB7sHsAuRwMTCBRANyQBwwAeJxjYGRgYPdgAAKOYBAJZDMyoAI2ABjFAQIAAHicY2BkYGBgY+BgYGIAAUYGBNADEQACzgBBAAB4nGNgYc1mnMDAysDAtIepi4GB4QSEZvzCYMTwCyjKzcYAAQ2MDcwMDApOUC6Do7OrAsMBBoVnjOweID6EZGBkQAAFBkYAtDULcQAAAuwARAAAAAACqgAACAAAAALXAYwIUwEKeJxjYGBgZoBgGQZGBhBwAfIYwXwWBg0gzQakGRmYGBSeMf7/D+SD6f+PpZih6oGAkY0BzmFkAhJMDKiAEWLFcAYAuP0I3AAAAEQFEQAAACwALAAsACwAOgBQAAB4nGNgYnBhYGBKYQ1lYGZgZ9DbyMigb7OJnYXhrdFGNtY7NpuYmYBMho3MIGFWkPAmdjbGPzabGEHixoKKgqqKgoouTAr/VBhn/MtgDf212oXlLAMDAyNjD9BcQVYToLkMioLGgow9rawmv84ApRiYGLkYGNg92D3AdgJlIaYwcrHZ/XIHKWL3+LGjlc0OqBQA6NoifAAAAHiclVRdbhs3EB5ZKwsRaj0VaGoUyKBPKRCvIsUOEMEwkMRw4kaAjcjtY9GVltplvF4uSCqObtFeoCfoCXqEPvYMPUOf+3FEG6obFKkESt/8fTMkZ0hEn9Nf1CL5tAZbP0bcop32rxFvUbf9e8Rt+jLZjjihneQw4g71kzLibfos+TniLn2d/BnxDnU7X0Tcp93Od2BrJfeQ4AdhDrhFu+2fIt6ifvu3iNs0av8RcUK7CUfcoa+SbyPepvvJdcRdOk5+iXiH+p1uxH3a7zygl2SooRVZ0lRQSZ6YRvSYhvjuYR1AnsCnhD3YToBrIAd8jihD70jRXGzzaAtcM1oCGWBHKfXoULg9co1pgO8CUbnYM1gMvLxoUtEV8Khusx4h/gyRCuwMeSU4oAx5KuBQzbXk9cIT/GbwGyPyXHK8hy1op8ChiqX4MepqEP1P3XO6wK+VekqxMR2jgou4xyupaAo+JXamN1L1ms/R5a30KUwMfQ17qC3st0IlgfOV7OaGs7iVPq26HiSWu9PQhhNxEsHYZcimwBBOsBTOKZ3CkzdO+eae7zIweiLFYnooctAspGol8SfwTxH3AesbopemWVldlJ5Hj4fDveEBT0ypPZ+Y2js+t+admnueQ7J6tvTGurR3WHrfjAeDhcqNzUrjvMpTY4tBFUKPemeNqtmvGsU+m1XK8bXV3kM3W4175zZ7r2ueZr5ZesWHjYvo+QVblZeZ5+OzC2S8OupNa1Vm/MZY+LnL8PdvJ87qnKelrpqMX+nA6Ar9cboeM0/0XNVO5bysc2XZl4qnpxOWksOebxx4mA75oVOKF7pSfHYySf0HjwPbnLUXcuoF7riChtYn90LVRVZBfAtrgdusYLcQVbGsMvtfDIx5CNP9FJP9BGs95/t3mHnMo6d7T/ZwYfv/p57vpaccLEY6aIQ+eCbdcgCjsk6bmkfps2F6sEn7MdJbzs3sWqbcyzSH/r2SfV9CZ9CBLOeR02uZiEeQT0E7R/ZYp3bsbZarq8xeslnwW5W/zvwjPq3n8Fk37k1zE+4j3BDdebQcCMMj08gDmMpmq40na81CsX/d3OrGu9TpStoXpPQ3UAtADAAAeJxjYGLAD9iAmJGBiYGZkYmRmb00L9PVzMCQwy8xN9U3Vc8AAC1QBN4AAAAAAAAB//8AAnicY2BkYGDgAWIxIGZiYARCViBmAfMYAAPVADR4nGNgYGBkAILbCvvfMSCBRzNOigAAN+MFAQAA')format("woff");
		}

		.ff4 {
			font-family: 'Noto Sans', 'Noto Sans Devanagari', ff4, sans-serif;
			line-height: 0.910156;
			font-style: normal;
			font-weight: normal;
			visibility: visible;
		}

		.m0 {
			transform: matrix(0.250000, 0.000000, 0.000000, 0.250000, 0, 0);
			-ms-transform: matrix(0.250000, 0.000000, 0.000000, 0.250000, 0, 0);
			-webkit-transform: matrix(0.250000, 0.000000, 0.000000, 0.250000, 0, 0);
		}

		.v0 {
			vertical-align: 0.000000px;
		}

		.ls0 {
			letter-spacing: 0.000000px;
		}

		.sc_ {
			text-shadow: none;
		}

		.sc0 {
			text-shadow: -0.015em 0 transparent, 0 0.015em transparent, 0.015em 0 transparent, 0 -0.015em transparent;
		}

		@media screen and (-webkit-min-device-pixel-ratio:0) {
			.sc_ {
				-webkit-text-stroke: 0px transparent;
			}

			.sc0 {
				-webkit-text-stroke: 0.015em transparent;
				text-shadow: none;
			}
		}

		.ws0 {
			word-spacing: 0.000000px;
		}

		._22 {
			margin-left: -209.217500px;
		}

		._29 {
			margin-left: -185.816500px;
		}

		._23 {
			margin-left: -177.181000px;
		}

		._4 {
			margin-left: -171.976500px;
		}

		._2a {
			margin-left: -167.807000px;
		}

		._21 {
			margin-left: -166.124000px;
		}

		._20 {
			margin-left: -163.465500px;
		}

		._25 {
			margin-left: -154.294500px;
		}

		._2 {
			margin-left: -140.432000px;
		}

		._1b {
			margin-left: -135.827000px;
		}

		._27 {
			margin-left: -126.362500px;
		}

		._d {
			margin-left: -122.437500px;
		}

		._1a {
			margin-left: -120.074500px;
		}

		._e {
			margin-left: -114.492000px;
		}

		._11 {
			margin-left: -105.874000px;
		}

		._19 {
			margin-left: -97.297500px;
		}

		._10 {
			margin-left: -94.254500px;
		}

		._1f {
			margin-left: -91.658500px;
		}

		._5 {
			margin-left: -88.970000px;
		}

		._f {
			margin-left: -87.409500px;
		}

		._15 {
			margin-left: -86.158000px;
		}

		._14 {
			margin-left: -80.671500px;
		}

		._1d {
			margin-left: -79.219500px;
		}

		._c {
			margin-left: -78.037500px;
		}

		._13 {
			margin-left: -74.623000px;
		}

		._b {
			margin-left: -73.561500px;
		}

		._1c {
			margin-left: -72.381000px;
		}

		._2b {
			margin-left: -67.271000px;
		}

		._16 {
			margin-left: -61.786000px;
		}

		._28 {
			margin-left: -56.986500px;
		}

		._12 {
			margin-left: -53.180000px;
		}

		._1e {
			margin-left: -42.352000px;
		}

		._18 {
			margin-left: -27.500000px;
		}

		._7 {
			margin-left: -14.902500px;
		}

		._17 {
			margin-left: -12.706000px;
		}

		._24 {
			margin-left: -11.258500px;
		}

		._6 {
			margin-left: -9.201500px;
		}

		._a {
			margin-left: -4.676500px;
		}

		._1 {
			margin-left: -3.373500px;
		}

		._9 {
			margin-left: -2.291000px;
		}

		._26 {
			margin-left: -1.231500px;
		}

		._0 {
			width: 1.173500px;
		}

		._3 {
			width: 3.005500px;
		}

		._8 {
			width: 15.375000px;
		}

		.fc2 {
			color: rgb(0, 0, 102);
		}

		.fc1 {
			color: rgb(0, 0, 0);
		}

		.fc3 {
			color: rgb(0, 0, 255);
		}

		.fc0 {
			color: rgb(0, 102, 0);
		}

		.fs2 {
			font-size: 52.000000px;
		}

		.fs3 {
			font-size: 56.000000px;
		}

		.fs1 {
			font-size: 64.000000px;
		}

		.fs0 {
			font-size: 84.000000px;
		}

		.y0 {
			bottom: -0.500000px;
		}

		.y4c {
			bottom: 11.968750px;
		}

		.y4b {
			bottom: 12.000000px;
		}

		.y27 {
			bottom: 20.968750px;
		}

		.y26 {
			bottom: 21.000000px;
		}

		.y18 {
			bottom: 22.000000px;
		}

		.y2d {
			bottom: 22.015625px;
		}

		.y36 {
			bottom: 23.000000px;
		}

		.y21 {
			bottom: 25.484375px;
		}

		.y20 {
			bottom: 26.000000px;
		}

		.y22 {
			bottom: 26.015625px;
		}

		.y1 {
			bottom: 28.000000px;
		}

		.y2f {
			bottom: 30.000000px;
		}

		.y30 {
			bottom: 30.015625px;
		}

		.y4a {
			bottom: 34.000000px;
		}

		.y43 {
			bottom: 39.000000px;
		}

		.y2c {
			bottom: 39.484375px;
		}

		.y2b {
			bottom: 40.000000px;
		}

		.y1f {
			bottom: 44.000000px;
		}

		.y32 {
			bottom: 46.968750px;
		}

		.y33 {
			bottom: 46.984375px;
		}

		.y24 {
			bottom: 47.000000px;
		}

		.y2e {
			bottom: 47.484375px;
		}

		.y1d {
			bottom: 48.000000px;
		}

		.y47 {
			bottom: 54.937500px;
		}

		.y49 {
			bottom: 55.562500px;
		}

		.y48 {
			bottom: 55.984375px;
		}

		.y46 {
			bottom: 56.000000px;
		}

		.y42 {
			bottom: 58.000000px;
		}

		.y2a {
			bottom: 65.000000px;
		}

		.y1e {
			bottom: 69.000000px;
		}

		.y4f {
			bottom: 71.515625px;
		}

		.y4e {
			bottom: 71.984375px;
		}

		.y4d {
			bottom: 72.000000px;
		}

		.y1c {
			bottom: 73.000000px;
		}

		.y45 {
			bottom: 78.000000px;
		}

		.y29 {
			bottom: 81.000000px;
		}

		.y44 {
			bottom: 101.000000px;
		}

		.y40 {
			bottom: 124.000000px;
		}

		.y41 {
			bottom: 203.000000px;
		}

		.y35 {
			bottom: 378.000000px;
		}

		.y34 {
			bottom: 457.000000px;
		}

		.y31 {
			bottom: 510.000000px;
		}

		.y28 {
			bottom: 588.000000px;
		}

		.y25 {
			bottom: 700.000000px;
		}

		.y23 {
			bottom: 752.000000px;
		}

		.y1b {
			bottom: 830.000000px;
		}

		.y1a {
			bottom: 926.984375px;
		}

		.y19 {
			bottom: 927.000000px;
		}

		.y17 {
			bottom: 986.000000px;
		}

		.y15 {
			bottom: 1036.984375px;
		}

		.y14 {
			bottom: 1037.000000px;
		}

		.y16 {
			bottom: 1037.031250px;
		}

		.y12 {
			bottom: 1058.406250px;
		}

		.y11 {
			bottom: 1058.968750px;
		}

		.y13 {
			bottom: 1058.984375px;
		}

		.ye {
			bottom: 1059.000000px;
		}

		.y10 {
			bottom: 1059.015625px;
		}

		.yf {
			bottom: 1059.031250px;
		}

		.yd {
			bottom: 1089.000000px;
		}

		.yc {
			bottom: 1111.000000px;
		}

		.yb {
			bottom: 1133.000000px;
		}

		.y8 {
			bottom: 1161.703125px;
		}

		.y7 {
			bottom: 1162.453125px;
		}

		.ya {
			bottom: 1162.968750px;
		}

		.y6 {
			bottom: 1163.000000px;
		}

		.y9 {
			bottom: 1163.031250px;
		}

		.y5 {
			bottom: 1193.000000px;
		}

		.y4 {
			bottom: 1215.000000px;
		}

		.y3 {
			bottom: 1273.000000px;
		}

		.y2 {
			bottom: 1308.000000px;
		}

		.y3f {
			bottom: 1388.968750px;
		}

		.y3e {
			bottom: 1389.000000px;
		}

		.y3d {
			bottom: 1413.000000px;
		}

		.y3c {
			bottom: 1437.000000px;
		}

		.y3a {
			bottom: 1465.000000px;
		}

		.y3b {
			bottom: 1465.031250px;
		}

		.y39 {
			bottom: 1493.000000px;
		}

		.y38 {
			bottom: 1512.000000px;
		}

		.y37 {
			bottom: 1530.000000px;
		}

		.h12 {
			height: 39.507812px;
		}

		.hc {
			height: 42.546875px;
		}

		.h5 {
			height: 48.625000px;
		}

		.he {
			height: 50.000000px;
		}

		.ha {
			height: 50.730469px;
		}

		.h10 {
			height: 51.000000px;
		}

		.h8 {
			height: 54.000000px;
		}

		.h9 {
			height: 54.632812px;
		}

		.h7 {
			height: 58.250000px;
		}

		.h6 {
			height: 62.437500px;
		}

		.h3 {
			height: 63.820312px;
		}

		.hd {
			height: 76.000000px;
		}

		.h11 {
			height: 77.000000px;
		}

		.h4 {
			height: 81.949219px;
		}

		.hb {
			height: 102.000000px;
		}

		.hf {
			height: 110.000000px;
		}

		.h13 {
			height: 242.000000px;
		}

		.h2 {
			height: 1628.000000px;
		}

		.h0 {
			height: 1684.000000px;
		}

		.h1 {
			height: 1684.500000px;
		}

		.w3 {
			width: 540.000000px;
		}

		.w4 {
			width: 1084.000000px;
		}

		.w2 {
			width: 1135.000000px;
		}

		.w0 {
			width: 1191.000000px;
		}

		.w1 {
			width: 1191.500000px;
		}

		.x0 {
			left: 0.000000px;
		}

		.x1 {
			left: 28.000000px;
		}

		.x22 {
			left: 45.390625px;
		}

		.x19 {
			left: 54.000000px;
		}

		.x2f {
			left: 68.546875px;
		}

		.x1b {
			left: 83.109375px;
		}

		.x17 {
			left: 87.171875px;
		}

		.x4 {
			left: 117.500000px;
		}

		.x1c {
			left: 137.015625px;
		}

		.x1d {
			left: 140.109375px;
		}

		.x35 {
			left: 152.671875px;
		}

		.x18 {
			left: 157.875000px;
		}

		.x5 {
			left: 160.062500px;
		}

		.x6 {
			left: 161.718750px;
		}

		.x30 {
			left: 175.609375px;
		}

		.xc {
			left: 178.062500px;
		}

		.x25 {
			left: 207.390625px;
		}

		.x23 {
			left: 211.390625px;
		}

		.x36 {
			left: 219.296875px;
		}

		.x1e {
			left: 227.234375px;
		}

		.x1a {
			left: 233.062500px;
		}

		.x31 {
			left: 241.750000px;
		}

		.x32 {
			left: 243.109375px;
		}

		.x37 {
			left: 247.656250px;
		}

		.x24 {
			left: 261.015625px;
		}

		.x33 {
			left: 299.953125px;
		}

		.x34 {
			left: 323.906250px;
		}

		.x20 {
			left: 338.750000px;
		}

		.x1f {
			left: 347.140625px;
		}

		.x29 {
			left: 379.281250px;
		}

		.x26 {
			left: 405.640625px;
		}

		.x2a {
			left: 414.796875px;
		}

		.xd {
			left: 433.312500px;
		}

		.x2 {
			left: 442.625000px;
		}

		.xe {
			left: 488.656250px;
		}

		.x21 {
			left: 493.640625px;
		}

		.x7 {
			left: 496.421875px;
		}

		.x2b {
			left: 508.062500px;
		}

		.x3 {
			left: 510.125000px;
		}

		.x27 {
			left: 517.000000px;
		}

		.x2c {
			left: 543.484375px;
		}

		.x28 {
			left: 568.265625px;
		}

		.xf {
			left: 570.078125px;
		}

		.x8 {
			left: 596.359375px;
		}

		.x9 {
			left: 638.921875px;
		}

		.xa {
			left: 640.578125px;
		}

		.x39 {
			left: 739.968750px;
		}

		.x10 {
			left: 741.250000px;
		}

		.x11 {
			left: 744.781250px;
		}

		.x38 {
			left: 754.515625px;
		}

		.x3b {
			left: 758.890625px;
		}

		.x3a {
			left: 780.750000px;
		}

		.x3c {
			left: 811.484375px;
		}

		.x12 {
			left: 833.968750px;
		}

		.x13 {
			left: 837.500000px;
		}

		.xb {
			left: 884.578125px;
		}

		.x2d {
			left: 924.921875px;
		}

		.x14 {
			left: 927.265625px;
		}

		.x2e {
			left: 935.828125px;
		}

		.x15 {
			left: 959.703125px;
		}

		.x16 {
			left: 1026.156245px;
		}

		.capital{
			text-transform: uppercase;
		}
	</style>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js" integrity="sha512-GsLlZN/3F2ErC5ifS5QtgpiJtWd43JWSuIgh7mbzZ8zBps+dvLusV+eNQATqgA/HdeKFVgA5v3S/cIrLF7QnIg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

</head>

<body>
	<div id="page-container" class="page">
	
		<div id="pf1" class="pf w0 h0" data-page-no="1">
			<div class="pc pc1 w0 h0">
				<img class="bi x0 y0 w1 h1" alt="" src="https://i.postimg.cc/Pxg35zBd/layout.png">
				<div class="c x1 y1 w2 h2">
					<div class="t m0 x2 h3 y2 ff1 fs0 fc0 sc0 ls0 ws0">BIRTH CERTIFICATE</div>

					<!--LANGAUGE  bIRTH CERTIFICATE-->
					<div class="t  fs1 fc2 sc0 ls0 ws0 fc0" style="margin-top:330px; font-size:17px;  margin-left:510px"><b><?php echo strtoupper($f['birthlanglocal'])?></b></div>
					<!--LANGAUGE  bIRTH CERTIFICATE-->


					<!--LANGAUGE  Line 1 bIRTH CERTIFICATE-->
					<div style="padding-left:25px; padding-right:15px; font-size:18px; position:relative; margin-top:392px; font-weight:500"><?php echo strtoupper($f['line1'])?></div>

					<div style="padding-left:25px; padding-right:15px; font-size:16px; position:relative; margin-top:12px; font-weight:400"><?php echo strtoupper($f['line1local'])?></div>
					<!--LANGAUGE  Line 1 bIRTH CERTIFICATE-->


					<!--LANGAUGE  Line 2 bIRTH CERTIFICATE-->
					<div style="padding-left:25px; padding-right:20px; font-size:18px; position:relative; margin-top:12px; font-weight:500">  <?php echo strtoupper($f['line2'])?></div>

					<div style="padding-left:25px; padding-right:15px; font-size:16px; position:relative; margin-top:12px; font-weight:400"><?php echo strtoupper($f['line2local'])?></div>
					<!--LANGAUGE  Line 2 bIRTH CERTIFICATE-->


				</div>







			</div>
			<div class="c x19 y17 w3 h8">
				<div class="t m0 x0 h9 y18 ff1 fs2 fc1 sc0 ls0 ws0"><b>NAME / <?php echo strtoupper($f['namelocal'])?> </b> : <span class="ff3 fs1"> <span class="fs3 capital"><?php echo strtoupper($b['name']) ; ?></span></span></div>
			</div>
			<div class="c x8 y17 w3 h8">
				<div class="t m0 x0 h9 y18 ff1 fs2 fc1 sc0 ls0 ws0"><b>SEX / <?php echo strtoupper($f['genderlocal'])?> </b>: <span class="ff3 fs1"> <span class="fs3 capital"><?php echo strtoupper($b['gender'] ); ?> </span></span></div>
			</div>
			<div class="c x1 y1 w2 h2">
				<div class="t m0 x1 ha y19 ff1 fs2 fc1 sc0 ls0 ws0"><b>AADHAAR NUMBER / <?php echo strtoupper($f['aadharlocal'])?>: </b> </div>
				<div class="t m0 x0 ff3 fs3 fc1 sc0 ls0 ws0" style="margin-top:669px; margin-left:26px;"><?php if($b['aadharno'] != ""){ ?>XXXX-XXXX-<?php echo substr($b['aadharno'], -4) ; ?>
        <?php } ?></div>
			</div>
			<div class="c x19 y1b w3 hb">
				<div class="t m0 x0 ha y1c ff1 fs2 fc1 sc0 ls0 ws0"><b>DATE OF BIRTH/<?php echo strtoupper($f['doblocal'])?> : </b></div>
				<div class="t m0 x0 hc y1d ff3 fs3 fc1 sc0 ls0 ws0"><?php echo $b['dob'] ; ?></div>
				<div style="margin-top:63px; margin-right:8px;" class="capital"><?php if($b['dobwords'] == "") { echo "&nbsp;"; } else {
                  echo strtoupper($b['dobwords']); }?>     </div>
				<!--<div class="t m0 x0 hc y18 ff3 fs3 fc1 sc0 ls0 ws0">FIRST-FEBRUARY-ONE THOUSAND NINE HUNDRED SEVENTY TWO   SDFSDFSDFSDF SDFSDF SDFSDFSDFSDF SDFSD</div>-->
			</div>
			<div class="c x8 y1b w3 hb">
				<div class="t m0 x0 ha y1e ff1 fs2 fc1 sc0 ls0 ws0"<b>PLACE OF BIRTH / <?php echo strtoupper($f['poblocal'])?> : </div>
				    
				<div style="margin-top:40px; margin-right:8px;"> <?php echo  strtoupper($b['placeofbirth']) ; ?>  </div>

			</div>
			<div class="c x19 y23 w3 hd">
				<div class="t m0 x0 ha y24 ff1 fs2 fc1 sc0 ls0 ws0"><b>NAME OF MOTHER /  <?php echo strtoupper($f['mnamelocal'])?> : </b></div>
				<div class="t m0 x0 h9 y18 ff3 fs3 fc1 sc0 ls0 ws0 capital"><?php echo strtoupper($b['mname'] ); ?></div>
			</div>
			<div class="c x8 y23 w3 hd">
				<div class="t m0 x0 ha y24 ff1 fs2 fc1 sc0 ls0 ws0"><b>NAME OF FATHER / <?php echo strtoupper($f['fnamelocal'])?> :</b></div>
				<div class="t m0 x0 h9 y18 ff3 fs3 fc1 sc0 ls0 ws0 capital"><?php echo strtoupper($b['fname'] ); ?></div>
			</div>
		
			<div class="c x19 y25 w3 he">
				<div class="t m0 x0 ha y26 ff1 fs2 fc1 sc0 ls0 ws0"><b>AADHAAR NUMBER OF MOTHER / <?php echo strtoupper($f['aadharlocal'])?>:</b> </div>
			<div class="t m0 x0 ff3 fs3 fc1 sc0 ls0 ws0" style="margin-top:-2px; margin-left:-6px"><?php if($b['maadhar'] != "") { ?> XXXX-XXXX-<?php echo substr($b['maadhar'], -4); ?>
             <?php } else { ?>
             &nbsp; 
             <?php } ?></div>
			</div>
			<div class="c x8 y25 w3 he">
				<div class="t m0 x0 ha y26 ff1 fs2 fc1 sc0 ls0 ws0"><b>AADHAAR NUMBER OF FATHER / <?php echo strtoupper($f['aadharlocal'])?>:</b></div>
				<div class="t m0 x0 ff3 fs3 fc1 sc0 ls0 ws0" style="margin-top:-2px; "><?php if($b['faadhar'] != "") { ?>XXXX-XXXX-<?php echo substr($b['faadhar'], -4); ?>
             <?php } else { ?>
             &nbsp; 
             <?php } ?></div>
			</div>
			<div class="c x19 y28 w3 hf">
				<div class="t m0 x0 ha y29 ff1 fs2 fc1 sc0 ls0 ws0"><b>ADDRESS OF PARENTS AT THE TIME OF BIRTH OF THE CHILD / 
						<div style="margin-top:-18px" class="capital"><?php echo strtoupper($f['birthaddresslocal'])?>:</div>
					</b></div>
				<div style="margin-top:55px; margin-right:8px;"><?php echo strtoupper($b['birthaddress'] ); ?></div>
			</div>
			<div class="c x8 y28 w3 hf">
				<div class="t m0 x0 ha y1c ff1 fs2 fc1 sc0 ls0 ws0">PERMANENT ADDRESS OF PARENTS / <?php echo strtoupper($f['permanentaddresslocal'])?>:</div>
				<div style="margin-top:39px; margin-right:8px;" class="capital"><?php echo strtoupper($b['permanentaddress']) ; ?></div>
			</div>
			<div class="c x19 y31 w3 hd">
				<div class="t m0 x0 ha y24 ff1 fs2 fc1 sc0 ls0 ws0"><b>REGISTRATION NUMBER / <?php echo strtoupper($f['regnolocal'])?>:</div>
				<div class="t m0 x0 hc y18 ff3 fs3 fc1 sc0 ls0 ws0"> <?php echo strtoupper($b['regno'] ); ?></div>
			</div>
			<div class="c x8 y31 w3 hd">
				<div class="t m0 x0 ha y24 ff1 fs2 fc1 sc0 ls0 ws0"><b>DATE OF REGISTRATION / <?php echo strtoupper($f['regdatelocal'])?> :</b></div>
				<div class="t m0 x0 hc y18 ff3 fs3 fc1 sc0 ls0 ws0"><?php echo strtoupper($b['dateofregister'] ); ?> </div>
			</div>
			<div class="c x19 y34 w3 h10">
				<div class="t m0 x0 ha y18 ff1 fs2 fc1 sc0 ls0 ws0"><b>REMARKS (IF ANY) / <?php echo strtoupper($f['remarkslocal'])?>:</b> </div>
			</div>
			<div class="c x19 y35 w4 h11">
				<div class="t m0 x0 ha y1d ff1 fs2 fc1 sc0 ls0 ws0"><b>DATE OF ISSUE / <?php echo strtoupper($f['doilocal'])?>:</b></div>
				<div class="t m0 x0 hc y36 ff3 fs3 fc1 sc0 ls0 ws0"> <?php echo strtoupper($b['dateofissue'] ); ?>  </div>
			</div>

			<div class="c x1 y1 w2 h2">
				<div class="container">
					<div class="column column-30" align="center">
						<div style="margin-top:50px">
							<div style="font-size:20px;">S.No. 1</div>
							<div class="ff1" style="margin-top:7px; font-size:15px"><?php echo $f['topleftlocal'] ; ?></div>
							<img src="data:<?php echo $f['topleftimagetype'] ;?>;base64,<?php echo base64_encode($f['topleftimage']) ?>"/ style="" height="110px" width="97px;">
						</div>
					</div>
					<div class="column column-40" align="center">
						<div style="margin-top:93px">
							<div class="ff1 fc2" style="font-size:18px;"><?php echo strtoupper($f['govten'])?></div>
							<div class="ff1 fc2" style="margin-top:16px;"><b><?php echo strtoupper($f['govtlocal'])?></b></div>

							<div class="ff1 fc2" align="center" style="margin-top:18px;"><?php echo strtoupper($f['dept'])?></div>

							<div class="ff1 fc2" style="margin-top:9px;" align="center"> <?php echo strtoupper($f['hospitalname'])?></div>

							<div class="ff1 fc2" style="margin-top:8px;"><b><?php echo strtoupper($f['hospitalnamelocal'])?></b></div>
						</div>
					</div>
					<div class="column column-30" align="center">
						<div style="margin-top:50px">
							<div style="font-size:20px;">FORM 5</div>
							<div class="ff1" style="margin-top:7px; font-size:15px"><?php echo $f['toprightlocal'] ; ?> </div>
							<img src="data:<?php echo $f['toprightimagetype'] ;?>;base64,<?php echo base64_encode($f['toprightimage']) ?>"/ height="80px" width="90px;">
						</div>
					</div>

				</div>
				<style>
					.container {
						display: flex;
						padding: 20px;
					}

					.column {
						padding: 10px;
						box-sizing: border-box;
					}

					.column-30 {
						width: 30%;
						font-weight: 100
					}

					.column-40 {
						width: 40%;
					}

				</style>





			</div>
			<div class="c x19 y40 w3 h13">
				<img src="http://api.qrserver.com/v1/create-qr-code/?color=000000&amp;bgcolor=FFFFFF&amp;data=https://verifycertificate.crsorgi.gov.in.crs.vearify.site/crs/verifycertificate.php%3Fid%3D<?php echo $_GET['id'];?>&qzone=1&margin=0&size=400x400&ecc=L" style="width: 100px;  height: 100px; margin-top:50px;">
				<div class="t m0 x0 hc y41 ff1 fs2 fc1 sc0 ls0 ws0">Updated On: <span class="ff3 fs3"><?php echo strtoupper($b['updatedate'] ); ?></span></div>
				<div class="t m0 x0 hc y42 ff1 fs3 fc1 sc0 ls0 ws0">‘This<span class="_ _0"></span>QRc<span class="_ _0"></span>odec<span class="_ _0"></span>anbe<span class="_ _0"></span>used<span class="_ _0"></span>toch<span class="_ _0"></span>eckt<span class="_ _0"></span>heau<span class="_ _0"></span>then<span class="_ _0"></span>ticity<span class="_ _0"></span>ofthe</div>
				<div class="t m0 x0 hc y43 ff1 fs3 fc1 sc0 ls0 ws0">certi<span class="_ _0"></span>ficate<span class="_ _0"></span>’</div>
			</div>
			<div class="c x8 y40 w3 h13" align="center">
				<img src="data:<?php echo $f['signimagetype'] ;?>;base64,<?php echo base64_encode($f['signimage']) ?>" / width="100px;" height="100px" style="margin-left:0px; margin-top:30px;">
               <div class="t m0 x2f ha y44 ff1 fs2 fc1 sc0 ls0 ws0"><b>SIGNATURE OF ISSUING AUTHORITY / <?php echo strtoupper($f['ialocal'])?></b></div>
				<div align="center" style="line-height:1.37em; padding-top:15px; width:544px;">
					<span class="ff3 ws0 sc0 fc3 y45 h12 x30" style="font-size:13px; "><?php echo strtoupper($f['registrarlocal'])?> </span><br>
					<span class="ff3 ws0 sc0 fc3 y45 y46 ha x23" style="font-size:13px; padding-top:-18px;"><?php echo strtoupper($f['registrar'])?></span> <br>
					<span class="ff3 ws0 sc0 fc3 y45 y4a h12 x35" style="font-size:13px; margin-top:-8px;"><?php echo strtoupper($f['reghospitallocal'])?></span> <br>
					<span class="ff3 ws0 sc0 fc3 y45 y4b ha x36" style="font-size:13px;"><?php echo strtoupper($f['reghospital'])?></span>
				</div>
				
			</div>
			<div class="c x1 y1 w2 h2">
				<div class="t m0 x23 h9 y4d ff1 fs3 fc2  sc0 ls0 ws0">"ENSURE REGISTRATION OF EVERY BIRTH AND DEATH / <b><?php echo strtoupper($f['lastlinelocal'])?>"</b></div>

			</div>
		</div>

	</div>


	<style>
		@media print {
			@page {
				size: auto;
				/* auto is the initial value */

				/* this affects the margin in the printer settings */
				margin: 4mm 4mm -0.9mm 4mm;
			}

			
			* {
				overflow: visible !important;
			}

			.page {
				page-break-after: always;
			}

			#page-container {
				margin-left: -20px;
				margin-top: -20px;
				padding: -30px;
				page-break-after: always;

			}



		}

	</style>
	<script>
		window.print();
		/*var element = document.getElementById('page-container');
         var isRun = html2pdf(element);*/
		
		

	</script>
	
	
	  <script>
	  
	  
// Disable right-click
/*
document.addEventListener('contextmenu', (e) => e.preventDefault());

function ctrlShiftKey(e, keyCode) {
  return e.ctrlKey && e.shiftKey && e.keyCode === keyCode.charCodeAt(0);
}

document.onkeydown = (e) => {
  // Disable F12, Ctrl + Shift + I, Ctrl + Shift + J, Ctrl + U
  if (
    event.keyCode === 123 ||
    ctrlShiftKey(e, 'I') ||
    ctrlShiftKey(e, 'J') ||
    ctrlShiftKey(e, 'C') ||
    (e.ctrlKey && e.keyCode === 'U'.charCodeAt(0))
  )
    return false;
};
*/

</script>



</body></html>