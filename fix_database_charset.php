<?php
// <PERSON><PERSON><PERSON> to fix database character set and collation for Hindi text support
include('includes/database.php');

echo "<h2>Database Character Set Fix for Hindi Text</h2>\n";

// Set connection charset to UTF-8
mysqli_set_charset($conn, "utf8");
echo "✓ Connection charset set to UTF-8<br>\n";

// Get database name
$database = "birth";

// Fix database character set
$sql = "ALTER DATABASE `$database` CHARACTER SET utf8 COLLATE utf8_unicode_ci";
if (mysqli_query($conn, $sql)) {
    echo "✓ Database character set updated to UTF-8<br>\n";
} else {
    echo "✗ Error updating database charset: " . mysqli_error($conn) . "<br>\n";
}

// Get all tables in the database
$tables_query = "SHOW TABLES";
$tables_result = mysqli_query($conn, $tables_query);

if ($tables_result) {
    echo "<h3>Updating table character sets:</h3>\n";
    
    while ($table_row = mysqli_fetch_array($tables_result)) {
        $table_name = $table_row[0];
        
        // Update table character set
        $alter_table_sql = "ALTER TABLE `$table_name` CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_ci";
        
        if (mysqli_query($conn, $alter_table_sql)) {
            echo "✓ Table '$table_name' converted to UTF-8<br>\n";
        } else {
            echo "✗ Error converting table '$table_name': " . mysqli_error($conn) . "<br>\n";
        }
    }
} else {
    echo "✗ Error getting tables: " . mysqli_error($conn) . "<br>\n";
}

// Show current database and table character sets
echo "<h3>Current Character Set Status:</h3>\n";

// Show database charset
$db_charset_query = "SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
                     FROM information_schema.SCHEMATA 
                     WHERE SCHEMA_NAME = '$database'";
$db_charset_result = mysqli_query($conn, $db_charset_query);
if ($db_charset_result) {
    $db_charset = mysqli_fetch_assoc($db_charset_result);
    echo "Database charset: " . $db_charset['DEFAULT_CHARACTER_SET_NAME'] . 
         " (Collation: " . $db_charset['DEFAULT_COLLATION_NAME'] . ")<br>\n";
}

// Show table charsets
$table_charset_query = "SELECT TABLE_NAME, TABLE_COLLATION 
                        FROM information_schema.TABLES 
                        WHERE TABLE_SCHEMA = '$database'";
$table_charset_result = mysqli_query($conn, $table_charset_query);
if ($table_charset_result) {
    echo "<h4>Table Character Sets:</h4>\n";
    while ($table_charset = mysqli_fetch_assoc($table_charset_result)) {
        echo "Table '" . $table_charset['TABLE_NAME'] . "': " . 
             $table_charset['TABLE_COLLATION'] . "<br>\n";
    }
}

echo "<br><strong>Note:</strong> After running this script, you may need to re-insert any Hindi data that was corrupted. The database is now properly configured for UTF-8 support.<br>\n";

mysqli_close($conn);
?>
