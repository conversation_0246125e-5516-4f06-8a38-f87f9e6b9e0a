<?php
/**
 * <PERSON>ript to generate DejaVu Serif TTF files from source files
 * This script will create the TTF files needed for PDF generation
 */

// Check if FontForge is available (for Windows, you might need to install it)
function checkFontForge() {
    $output = [];
    $return_var = 0;
    exec('fontforge -version 2>&1', $output, $return_var);
    
    if ($return_var === 0) {
        echo "FontForge is available.\n";
        return true;
    } else {
        echo "FontForge is not available. Please install FontForge to generate TTF files.\n";
        echo "For Windows: Download from https://fontforge.org/en-US/downloads/windows/\n";
        return false;
    }
}

// Generate TTF files using FontForge
function generateTTF($sfdFile, $outputDir) {
    $command = "fontforge -lang=ff -c 'Open(\"$sfdFile\"); Generate(\"$outputDir/" . basename($sfdFile, '.sfd') . ".ttf\")'";
    
    echo "Generating TTF from: " . basename($sfdFile) . "\n";
    
    $output = [];
    $return_var = 0;
    exec($command . ' 2>&1', $output, $return_var);
    
    if ($return_var === 0) {
        echo "Successfully generated: " . basename($sfdFile, '.sfd') . ".ttf\n";
        return true;
    } else {
        echo "Failed to generate TTF from: " . basename($sfdFile) . "\n";
        echo "Error: " . implode("\n", $output) . "\n";
        return false;
    }
}

// Main execution
echo "DejaVu Font TTF Generator\n";
echo "========================\n\n";

if (!checkFontForge()) {
    exit(1);
}

$sourceDir = 'fonts/dejavu-fonts-master/dejavu-fonts-master/src';
$outputDir = 'fonts/dejavu-serif-ttf';

// Create output directory
if (!is_dir($outputDir)) {
    mkdir($outputDir, 0755, true);
    echo "Created output directory: $outputDir\n";
}

// DejaVu Serif font files we need
$serifFonts = [
    'DejaVuSerif.sfd',
    'DejaVuSerif-Bold.sfd',
    'DejaVuSerif-Italic.sfd',
    'DejaVuSerif-BoldItalic.sfd'
];

$success = 0;
$total = count($serifFonts);

foreach ($serifFonts as $font) {
    $sfdPath = $sourceDir . '/' . $font;
    
    if (file_exists($sfdPath)) {
        if (generateTTF($sfdPath, $outputDir)) {
            $success++;
        }
    } else {
        echo "Source file not found: $sfdPath\n";
    }
}

echo "\n";
echo "Generation complete: $success/$total fonts generated successfully.\n";

if ($success > 0) {
    echo "\nGenerated TTF files are available in: $outputDir\n";
    echo "You can now use these fonts in your PDF generation.\n";
} else {
    echo "\nNo fonts were generated. Please check FontForge installation and source files.\n";
}
?>
