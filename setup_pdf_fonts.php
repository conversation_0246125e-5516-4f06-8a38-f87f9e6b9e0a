<?php
/**
 * Setup script for PDF font configuration
 * This script will help you set up all the required fonts for PDF generation
 */

echo "PDF Font Setup Script\n";
echo "====================\n\n";

// Check PHP version
if (version_compare(PHP_VERSION, '7.0.0') < 0) {
    echo "Error: PHP 7.0 or higher is required.\n";
    exit(1);
}

// Function to check if Composer is available
function checkComposer() {
    $output = [];
    $return_var = 0;
    exec('composer --version 2>&1', $output, $return_var);
    
    if ($return_var === 0) {
        echo "✓ Composer is available.\n";
        return true;
    } else {
        echo "✗ Composer is not available. Please install Composer first.\n";
        echo "  Download from: https://getcomposer.org/\n";
        return false;
    }
}

// Function to install mPDF via Composer
function installMPDF() {
    echo "Installing mPDF via Composer...\n";
    
    // Create composer.json if it doesn't exist
    if (!file_exists('composer.json')) {
        $composerConfig = [
            'require' => [
                'mpdf/mpdf' => '^8.0'
            ]
        ];
        
        file_put_contents('composer.json', json_encode($composerConfig, JSON_PRETTY_PRINT));
        echo "Created composer.json\n";
    } else {
        echo "composer.json already exists. You may need to manually add 'mpdf/mpdf': '^8.0' to require section.\n";
    }
    
    $output = [];
    $return_var = 0;
    exec('composer install 2>&1', $output, $return_var);
    
    if ($return_var === 0) {
        echo "✓ mPDF installed successfully.\n";
        return true;
    } else {
        echo "✗ Failed to install mPDF.\n";
        echo "Error: " . implode("\n", $output) . "\n";
        return false;
    }
}

// Function to check font files
function checkFontFiles() {
    echo "\nChecking font files...\n";
    
    $fonts = [
        'Lohit Bengali' => 'fonts/Lohit-Bengali/Lohit-Bengali.ttf',
        'DejaVu Serif (source)' => 'fonts/dejavu-fonts-master/dejavu-fonts-master/src/DejaVuSerif.sfd'
    ];
    
    $allPresent = true;
    
    foreach ($fonts as $name => $path) {
        if (file_exists($path)) {
            echo "✓ $name found at: $path\n";
        } else {
            echo "✗ $name NOT found at: $path\n";
            $allPresent = false;
        }
    }
    
    return $allPresent;
}

// Function to create font directories
function createFontDirectories() {
    echo "\nCreating font directories...\n";
    
    $directories = [
        'fonts/dejavu-serif-ttf',
        'fonts/sakal-bharati'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "✓ Created directory: $dir\n";
            } else {
                echo "✗ Failed to create directory: $dir\n";
                return false;
            }
        } else {
            echo "✓ Directory already exists: $dir\n";
        }
    }
    
    return true;
}

// Function to provide setup instructions
function provideInstructions() {
    echo "\n" . str_repeat("=", 60) . "\n";
    echo "SETUP INSTRUCTIONS\n";
    echo str_repeat("=", 60) . "\n\n";
    
    echo "1. SAKAL BHARATI FONT:\n";
    echo "   - Download from: https://www.cdac.in/index.aspx?id=dl_sakal_bharati_font\n";
    echo "   - Extract the TTF file\n";
    echo "   - Place it as: fonts/sakal-bharati/SakalBharati.ttf\n\n";
    
    echo "2. DEJAVU SERIF FONTS:\n";
    echo "   - Run: php generate_dejavu_fonts.php\n";
    echo "   - This will generate TTF files from the source files\n";
    echo "   - Requires FontForge to be installed\n\n";
    
    echo "3. FONTFORGE INSTALLATION (for DejaVu fonts):\n";
    echo "   - Windows: Download from https://fontforge.org/en-US/downloads/windows/\n";
    echo "   - Linux: sudo apt-get install fontforge\n";
    echo "   - macOS: brew install fontforge\n\n";
    
    echo "4. UPDATE YOUR PDF GENERATION CODE:\n";
    echo "   - Replace phpToPDF calls with the new generatePDF() function\n";
    echo "   - Include the new pdf_generator.php file\n\n";
    
    echo "5. EXAMPLE USAGE:\n";
    echo "   require_once 'pdf_generator.php';\n";
    echo "   \$options = [\n";
    echo "       'source_type' => 'html',\n";
    echo "       'source' => \$html_content,\n";
    echo "       'action' => 'save',\n";
    echo "       'file_name' => 'document.pdf'\n";
    echo "   ];\n";
    echo "   generatePDF(\$options);\n\n";
}

// Main execution
echo "Starting PDF font setup...\n\n";

$success = true;

// Check Composer
if (!checkComposer()) {
    $success = false;
} else {
    // Install mPDF
    if (!installMPDF()) {
        $success = false;
    }
}

// Check existing font files
checkFontFiles();

// Create font directories
if (!createFontDirectories()) {
    $success = false;
}

// Provide instructions
provideInstructions();

if ($success) {
    echo "Setup completed successfully!\n";
    echo "Please follow the instructions above to complete the font setup.\n";
} else {
    echo "Setup completed with some issues.\n";
    echo "Please resolve the issues above before proceeding.\n";
}

echo "\nNext steps:\n";
echo "1. Download and place the Sakal Bharati font\n";
echo "2. Generate DejaVu TTF fonts using generate_dejavu_fonts.php\n";
echo "3. Update your existing PDF generation code\n";
?>
