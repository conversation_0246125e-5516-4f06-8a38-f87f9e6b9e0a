/* Footer */

.footer {
  background: $footer-bg;
  color: $footer-color;
  padding: 8px 40px 37px 40px;
  transition: all $action-transition-duration $action-transition-timing-function;
  -moz-transition: all $action-transition-duration $action-transition-timing-function;
  -webkit-transition: all $action-transition-duration $action-transition-timing-function;
  -ms-transition: all $action-transition-duration $action-transition-timing-function;
  font-size: calc(#{$default-font-size} - 0.05rem);
  font-family: $type1;
  font-weight: 400;
  a {
    color: theme-color(success);
    font-size: inherit;
  }
  @media (max-width:576px) {
    padding-top: 15px;
  }
  @media (max-width: 991px) {
    margin-left: 0;
    width: 100%;
  }
}
