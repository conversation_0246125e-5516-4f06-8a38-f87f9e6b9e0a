/* Tabs */

.add-items {
  margin-top: 8px;
  overflow: hidden;
  input[type="text"] {
    border-radius: 0;
    background: transparent;
    @media (max-width:560px) {
      width: 100%;
      margin-bottom: 10px;
    }
  }
  .btn {
    margin-left: .5rem;
    .rtl & {
      margin-left: auto;
      margin-right: .5rem;
    }
    @media (max-width:560px) {
      margin-left: 0;
    }
  }
}

.list-wrapper {
  max-height: 100%;
  ul {
    padding: 0;
    text-align: left;
    list-style: none;
    margin-bottom: 0;
    li {
      @extend .d-flex;
      @extend .align-items-center;
      @extend .justify-content-start;
      font-size: .9375rem;
      padding: 7px 0 6px 0;
      border-bottom: 1px solid $border-color;
      &:first-child {
        border-bottom: none;
      }
      .form-check {
        @extend %ellipsor;
        max-width: 90%;
        margin-top: .25rem;
        margin-bottom: .25rem;
        .form-check-label{
          @extend%ellipsor;
          font-weight: 500;
        }
        .list-time {
          font-size: 12px;
          color: $text-muted;
          font-weight: 300;
          margin-left: 30px;
          text-decoration: none;
          display: block;
        }
      }
    }
  }

  input[type="checkbox"] {
    margin-right: 15px;
  }

  .remove {
    @extend .ml-auto;
    @extend .text-secondary;
    .rtl & {
      @extend .ml-0;
      @extend .mr-auto;
    }
    cursor: pointer;
    font-size: 1.438rem;
    font-weight: 600;
    width: 1.25rem;
    height: 1.25rem;
    line-height: 20px;
    text-align: center;
  }

  .completed {
    text-decoration: line-through;
    text-decoration-color: theme-color(info);
    .remove {
      @extend .text-primary;
    }
  }
}
